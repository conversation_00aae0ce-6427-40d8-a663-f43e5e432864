# نظام إدارة الشركات
## Company Management System

نظام شامل لإدارة الشركات وفئات رأس المال والملاك مع واجهة عربية متجاوبة.

## 🌟 الميزات الرئيسية

### 📊 إدارة فئات رأس المال
- إنشاء فئات مختلفة لرأس المال (10,000، 25,000، 50,000، إلخ)
- تحديد الحد الأقصى للشركات في كل فئة
- عرض إحصائيات مفصلة لكل فئة

### 🏢 إدارة الشركات
- إضافة شركات جديدة مع خطط المشاريع التفصيلية
- ربط الشركات بفئات رأس المال المناسبة
- تحديد الموارد وطرق العمل لكل شركة
- تعديل بيانات الشركات

### 👥 إدارة الأشخاص والملاك
- إضافة الأشخاص مع بياناتهم الكاملة
- ربط الملاك بالشركات (مالك أساسي + ملاك إضافيين)
- تتبع نسب الملكية والأدوار

### 📁 إدارة الملفات
- رفع الملفات والمستندات لكل شركة
- دعم أنواع مختلفة من الملفات (PDF, DOC, صور)
- وصف وتصنيف الملفات

### 🔐 نظام المصادقة
- تسجيل دخول آمن
- إدارة المستخدمين
- صلاحيات متدرجة

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.8 أو أحدث
- pip (مدير حزم Python)

### خطوات التثبيت

1. **تحميل المشروع**
   ```bash
   # إذا كان لديك git
   git clone <repository-url>
   cd rt
   ```

2. **تثبيت المكتبات المطلوبة**
   ```bash
   pip install -r requirements.txt
   ```

3. **تشغيل التطبيق**
   ```bash
   python run.py
   ```

4. **فتح التطبيق في المتصفح**
   ```
   http://localhost:5000
   ```

### معلومات تسجيل الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## 📱 واجهة المستخدم

### الصفحة الرئيسية
- لوحة تحكم شاملة مع إحصائيات سريعة
- عرض أحدث الشركات المضافة
- إجراءات سريعة للمهام الشائعة

### إدارة فئات رأس المال
- عرض جميع الفئات مع إحصائياتها
- شريط تقدم لمعرفة نسبة الإشغال
- إمكانية إضافة فئات جديدة

### إدارة الشركات
- عرض الشركات مجمعة حسب فئات رأس المال
- تفاصيل شاملة لكل شركة
- إمكانية تعديل البيانات ورفع الملفات

### إدارة الأشخاص
- قائمة شاملة بجميع الأشخاص
- بحث وفلترة متقدمة
- عرض الشركات المرتبطة بكل شخص

## 🗂️ هيكل المشروع

```
rt/
├── app.py              # التطبيق الرئيسي
├── models.py           # نماذج قاعدة البيانات
├── config.py           # إعدادات التطبيق
├── run.py              # ملف التشغيل
├── requirements.txt    # المكتبات المطلوبة
├── templates/          # قوالب HTML
│   ├── base.html
│   ├── dashboard.html
│   ├── capital_categories.html
│   ├── company_details.html
│   └── ...
├── static/             # الملفات الثابتة
│   └── css/
│       └── style.css
└── uploads/            # الملفات المرفوعة
```

## 🛠️ التقنيات المستخدمة

### Backend
- **Flask** - إطار عمل Python للويب
- **SQLAlchemy** - ORM لقاعدة البيانات
- **Flask-Login** - إدارة جلسات المستخدمين
- **Flask-WTF** - معالجة النماذج
- **SQLite** - قاعدة البيانات (افتراضياً)

### Frontend
- **Bootstrap 5** - إطار عمل CSS
- **Bootstrap Icons** - الأيقونات
- **JavaScript** - التفاعل والديناميكية
- **RTL Support** - دعم اللغة العربية

## 📊 قاعدة البيانات

### الجداول الرئيسية
- **users** - المستخدمين
- **capital_category** - فئات رأس المال
- **person** - الأشخاص
- **company** - الشركات
- **company_ownership** - ملكية الشركات
- **company_file** - ملفات الشركات
- **system_backup** - النسخ الاحتياطية

## 🔧 الإعدادات

### متغيرات البيئة
يمكنك إنشاء ملف `.env` لتخصيص الإعدادات:

```env
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///companies.db
FLASK_ENV=development
```

### إعدادات رفع الملفات
- **الحد الأقصى لحجم الملف:** 16 MB
- **الأنواع المدعومة:** PDF, DOC, DOCX, JPG, PNG, GIF, TXT, XLS, XLSX

## 🚀 الميزات المستقبلية

- [ ] النسخ الاحتياطي التلقائي
- [ ] تقارير مفصلة وإحصائيات
- [ ] إشعارات البريد الإلكتروني
- [ ] API للتكامل مع أنظمة أخرى
- [ ] دعم قواعد بيانات أخرى (PostgreSQL, MySQL)
- [ ] نظام الأذونات المتقدم
- [ ] تصدير البيانات (Excel, PDF)

## 🐛 الإبلاغ عن المشاكل

إذا واجهت أي مشاكل أو لديك اقتراحات للتحسين، يرجى:
1. التأكد من تثبيت جميع المتطلبات
2. مراجعة ملف السجلات (logs)
3. إنشاء issue جديد مع تفاصيل المشكلة

## 📝 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## 👨‍💻 المطور

تم تطوير هذا النظام لإدارة الشركات وفئات رأس المال بواجهة عربية سهلة الاستخدام.

---

**ملاحظة:** هذا النظام مصمم للاستخدام المحلي أو في بيئة آمنة. للاستخدام في الإنتاج، يرجى مراجعة إعدادات الأمان وقاعدة البيانات.