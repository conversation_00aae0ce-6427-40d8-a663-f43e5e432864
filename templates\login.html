{% extends "base.html" %}

{% block title %}تسجيل الدخول - نظام إدارة الشركات{% endblock %}

{% block content %}
<div class="row justify-content-center min-vh-100 align-items-center">
    <div class="col-md-6 col-lg-4">
        <div class="text-center mb-4 fade-in-up">
            <i class="bi bi-building-gear display-1 text-primary mb-3"></i>
            <h2 class="fw-bold text-primary">نظام إدارة الشركات</h2>
            <p class="text-muted">مرحباً بك في نظام إدارة الشركات المتطور</p>
        </div>
        
        <div class="card shadow-lg border-0 fade-in-up" style="animation-delay: 0.2s;">
            <div class="card-header text-center bg-gradient bg-primary text-white py-4">
                <h4 class="mb-0 fw-bold">
                    <i class="bi bi-shield-lock me-2"></i>
                    تسجيل الدخول
                </h4>
            </div>
            <div class="card-body p-4">
                <form method="POST" id="loginForm">
                    <div class="mb-4">
                        <label for="username" class="form-label fw-bold text-dark">
                            <i class="bi bi-person-circle text-primary me-2"></i>
                            اسم المستخدم
                        </label>
                        <div class="input-group input-group-lg">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="bi bi-person text-primary"></i>
                            </span>
                            <input type="text" class="form-control border-start-0 ps-0" 
                                   id="username" name="username" 
                                   placeholder="أدخل اسم المستخدم" required>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="password" class="form-label fw-bold text-dark">
                            <i class="bi bi-shield-lock text-primary me-2"></i>
                            كلمة المرور
                        </label>
                        <div class="input-group input-group-lg">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="bi bi-lock text-primary"></i>
                            </span>
                            <input type="password" class="form-control border-start-0 ps-0" 
                                   id="password" name="password" 
                                   placeholder="أدخل كلمة المرور" required>
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="bi bi-eye" id="toggleIcon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-primary btn-lg py-3" id="loginBtn">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            تسجيل الدخول
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer bg-light text-center py-3">
                <div class="alert alert-info mb-0 border-0 bg-transparent">
                    <i class="bi bi-info-circle text-info me-2"></i>
                    <strong>بيانات الدخول الافتراضية:</strong><br>
                    <div class="mt-2">
                        <span class="badge bg-primary me-2">المستخدم: admin</span>
                        <span class="badge bg-success">كلمة المرور: admin123</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="text-center mt-4 fade-in-up" style="animation-delay: 0.4s;">
            <div class="row text-center">
                <div class="col-4">
                    <i class="bi bi-shield-check text-success fs-3"></i>
                    <p class="small text-muted mt-1">آمن ومحمي</p>
                </div>
                <div class="col-4">
                    <i class="bi bi-speedometer2 text-primary fs-3"></i>
                    <p class="small text-muted mt-1">سريع وفعال</p>
                </div>
                <div class="col-4">
                    <i class="bi bi-phone text-info fs-3"></i>
                    <p class="small text-muted mt-1">متجاوب</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تبديل إظهار/إخفاء كلمة المرور
    const togglePassword = document.getElementById('togglePassword');
    const passwordField = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');
    
    togglePassword.addEventListener('click', function() {
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);
        
        if (type === 'text') {
            toggleIcon.className = 'bi bi-eye-slash';
        } else {
            toggleIcon.className = 'bi bi-eye';
        }
    });
    
    // تحسين تجربة النموذج
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    
    loginForm.addEventListener('submit', function(e) {
        // عرض حالة التحميل
        const originalText = loginBtn.innerHTML;
        loginBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري تسجيل الدخول...';
        loginBtn.disabled = true;
        
        // إعادة تفعيل الزر في حالة فشل الإرسال
        setTimeout(() => {
            loginBtn.innerHTML = originalText;
            loginBtn.disabled = false;
        }, 5000);
    });
    
    // تأثيرات التفاعل للحقول
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('shadow-sm');
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('shadow-sm');
            this.parentElement.style.transform = 'scale(1)';
        });
    });
    
    // ملء البيانات الافتراضية عند النقر على البادج
    document.querySelectorAll('.badge').forEach(badge => {
        badge.style.cursor = 'pointer';
        badge.addEventListener('click', function() {
            if (this.textContent.includes('admin')) {
                document.getElementById('username').value = 'admin';
            } else if (this.textContent.includes('admin123')) {
                document.getElementById('password').value = 'admin123';
            }
        });
    });
});
</script>
{% endblock %}