#!/usr/bin/env python3
"""
معلومات النظام والإحصائيات
System Information and Statistics
"""

import os
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
import json

class SystemInfo:
    def __init__(self, app_root=None):
        self.app_root = Path(app_root) if app_root else Path(__file__).parent
        self.db_path = self.app_root / 'companies.db'
    
    def get_database_stats(self):
        """إحصائيات قاعدة البيانات"""
        if not self.db_path.exists():
            return None
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        stats = {}
        
        # إحصائيات المستخدمين
        cursor.execute("SELECT COUNT(*) FROM user")
        stats['total_users'] = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM user WHERE is_admin = 1")
        stats['admin_users'] = cursor.fetchone()[0]
        
        # إحصائيات فئات رأس المال
        cursor.execute("SELECT COUNT(*) FROM capital_category WHERE is_active = 1")
        stats['active_categories'] = cursor.fetchone()[0]
        
        cursor.execute("SELECT SUM(max_companies) FROM capital_category WHERE is_active = 1")
        result = cursor.fetchone()[0]
        stats['total_capacity'] = result if result else 0
        
        # إحصائيات الشركات
        cursor.execute("SELECT COUNT(*) FROM company WHERE is_active = 1")
        stats['active_companies'] = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM company")
        stats['total_companies'] = cursor.fetchone()[0]
        
        cursor.execute("SELECT status, COUNT(*) FROM company GROUP BY status")
        status_counts = cursor.fetchall()
        stats['companies_by_status'] = {status: count for status, count in status_counts}
        
        # إحصائيات الأشخاص
        cursor.execute("SELECT COUNT(*) FROM person")
        stats['total_people'] = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT primary_owner_id) FROM company")
        stats['people_with_companies'] = cursor.fetchone()[0]
        
        # إحصائيات الملفات
        cursor.execute("SELECT COUNT(*) FROM company_file")
        stats['total_files'] = cursor.fetchone()[0]
        
        cursor.execute("SELECT SUM(file_size) FROM company_file")
        result = cursor.fetchone()[0]
        stats['total_files_size'] = result if result else 0
        
        cursor.execute("SELECT file_type, COUNT(*) FROM company_file GROUP BY file_type")
        file_types = cursor.fetchall()
        stats['files_by_type'] = {file_type: count for file_type, count in file_types}
        
        # إحصائيات الملكية
        cursor.execute("SELECT COUNT(*) FROM company_ownership WHERE is_active = 1")
        stats['additional_ownerships'] = cursor.fetchone()[0]
        
        # الشركات المضافة حديثاً (آخر 30 يوم)
        thirty_days_ago = datetime.now() - timedelta(days=30)
        cursor.execute("SELECT COUNT(*) FROM company WHERE created_at > ?", (thirty_days_ago,))
        stats['recent_companies'] = cursor.fetchone()[0]
        
        # أكثر فئات رأس المال استخداماً
        cursor.execute("""
            SELECT cc.name, COUNT(c.id) as company_count
            FROM capital_category cc
            LEFT JOIN company c ON cc.id = c.capital_category_id AND c.is_active = 1
            GROUP BY cc.id, cc.name
            ORDER BY company_count DESC
            LIMIT 5
        """)
        stats['top_categories'] = cursor.fetchall()
        
        conn.close()
        return stats
    
    def get_system_health(self):
        """فحص صحة النظام"""
        health = {
            'status': 'healthy',
            'issues': [],
            'warnings': []
        }
        
        # فحص قاعدة البيانات
        if not self.db_path.exists():
            health['issues'].append('قاعدة البيانات غير موجودة')
            health['status'] = 'critical'
        else:
            db_size = self.db_path.stat().st_size
            if db_size > 100 * 1024 * 1024:  # 100MB
                health['warnings'].append(f'حجم قاعدة البيانات كبير: {db_size / (1024*1024):.1f} MB')
        
        # فحص مجلد الرفع
        uploads_dir = self.app_root / 'uploads'
        if not uploads_dir.exists():
            health['warnings'].append('مجلد الرفع غير موجود')
        else:
            # حساب حجم مجلد الرفع
            total_size = sum(f.stat().st_size for f in uploads_dir.rglob('*') if f.is_file())
            if total_size > 500 * 1024 * 1024:  # 500MB
                health['warnings'].append(f'حجم الملفات المرفوعة كبير: {total_size / (1024*1024):.1f} MB')
        
        # فحص النسخ الاحتياطية
        backups_dir = self.app_root / 'backups'
        if not backups_dir.exists():
            health['warnings'].append('مجلد النسخ الاحتياطية غير موجود')
        else:
            backups = list(backups_dir.glob('*.zip'))
            if len(backups) == 0:
                health['warnings'].append('لا توجد نسخ احتياطية')
            elif len(backups) > 20:
                health['warnings'].append(f'عدد كبير من النسخ الاحتياطية: {len(backups)}')
        
        # تحديد الحالة العامة
        if health['issues']:
            health['status'] = 'critical'
        elif health['warnings']:
            health['status'] = 'warning'
        
        return health
    
    def get_capacity_analysis(self):
        """تحليل السعة والاستخدام"""
        if not self.db_path.exists():
            return None
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT 
                cc.name,
                cc.max_companies,
                COUNT(c.id) as current_companies,
                cc.max_companies - COUNT(c.id) as available_slots,
                ROUND((COUNT(c.id) * 100.0 / cc.max_companies), 2) as usage_percentage
            FROM capital_category cc
            LEFT JOIN company c ON cc.id = c.capital_category_id AND c.is_active = 1
            WHERE cc.is_active = 1
            GROUP BY cc.id, cc.name, cc.max_companies
            ORDER BY usage_percentage DESC
        """)
        
        capacity_data = []
        for row in cursor.fetchall():
            capacity_data.append({
                'category': row[0],
                'max_companies': row[1],
                'current_companies': row[2],
                'available_slots': row[3],
                'usage_percentage': row[4]
            })
        
        conn.close()
        return capacity_data
    
    def export_stats_json(self, output_file=None):
        """تصدير الإحصائيات إلى ملف JSON"""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = self.app_root / f'system_stats_{timestamp}.json'
        
        stats = {
            'generated_at': datetime.now().isoformat(),
            'database_stats': self.get_database_stats(),
            'system_health': self.get_system_health(),
            'capacity_analysis': self.get_capacity_analysis()
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2, default=str)
        
        return output_file
    
    def print_summary_report(self):
        """طباعة تقرير ملخص"""
        print("=" * 60)
        print("🏢 تقرير نظام إدارة الشركات")
        print("=" * 60)
        print(f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # إحصائيات قاعدة البيانات
        stats = self.get_database_stats()
        if stats:
            print("📊 إحصائيات عامة:")
            print(f"   👥 المستخدمين: {stats['total_users']} (منهم {stats['admin_users']} مدير)")
            print(f"   💰 فئات رأس المال النشطة: {stats['active_categories']}")
            print(f"   🏢 الشركات النشطة: {stats['active_companies']} من {stats['total_companies']}")
            print(f"   👤 الأشخاص المسجلين: {stats['total_people']}")
            print(f"   📁 الملفات المرفوعة: {stats['total_files']} ({stats['total_files_size'] / (1024*1024):.1f} MB)")
            print()
            
            # حالة الشركات
            if stats['companies_by_status']:
                print("📈 حالة الشركات:")
                for status, count in stats['companies_by_status'].items():
                    status_ar = {'active': 'نشطة', 'suspended': 'معلقة', 'closed': 'مغلقة'}.get(status, status)
                    print(f"   - {status_ar}: {count}")
                print()
            
            # أنواع الملفات
            if stats['files_by_type']:
                print("📄 أنواع الملفات:")
                for file_type, count in stats['files_by_type'].items():
                    type_ar = {'image': 'صور', 'document': 'مستندات'}.get(file_type, file_type)
                    print(f"   - {type_ar}: {count}")
                print()
        
        # تحليل السعة
        capacity = self.get_capacity_analysis()
        if capacity:
            print("📊 تحليل السعة:")
            for cat in capacity:
                print(f"   💰 {cat['category']}: {cat['current_companies']}/{cat['max_companies']} ({cat['usage_percentage']:.1f}%)")
            print()
        
        # صحة النظام
        health = self.get_system_health()
        status_icon = {'healthy': '✅', 'warning': '⚠️', 'critical': '❌'}[health['status']]
        status_ar = {'healthy': 'سليم', 'warning': 'تحذير', 'critical': 'خطر'}[health['status']]
        
        print(f"🔍 صحة النظام: {status_icon} {status_ar}")
        
        if health['issues']:
            print("   ❌ مشاكل:")
            for issue in health['issues']:
                print(f"      - {issue}")
        
        if health['warnings']:
            print("   ⚠️ تحذيرات:")
            for warning in health['warnings']:
                print(f"      - {warning}")
        
        print("=" * 60)

def main():
    """الواجهة التفاعلية لمعلومات النظام"""
    system_info = SystemInfo()
    
    while True:
        print("\n" + "=" * 50)
        print("📊 معلومات النظام - نظام إدارة الشركات")
        print("=" * 50)
        print("1. عرض تقرير ملخص")
        print("2. إحصائيات قاعدة البيانات")
        print("3. تحليل السعة")
        print("4. فحص صحة النظام")
        print("5. تصدير الإحصائيات (JSON)")
        print("0. خروج")
        print("-" * 50)
        
        choice = input("اختر العملية المطلوبة: ").strip()
        
        try:
            if choice == '1':
                system_info.print_summary_report()
                
            elif choice == '2':
                stats = system_info.get_database_stats()
                if stats:
                    print("\n📊 إحصائيات قاعدة البيانات:")
                    for key, value in stats.items():
                        if isinstance(value, dict):
                            print(f"   {key}:")
                            for k, v in value.items():
                                print(f"      {k}: {v}")
                        elif isinstance(value, list):
                            print(f"   {key}:")
                            for item in value:
                                print(f"      {item}")
                        else:
                            print(f"   {key}: {value}")
                else:
                    print("❌ قاعدة البيانات غير متاحة")
                    
            elif choice == '3':
                capacity = system_info.get_capacity_analysis()
                if capacity:
                    print("\n📊 تحليل السعة:")
                    print("-" * 80)
                    print(f"{'الفئة':<25} {'الحالية':<8} {'الأقصى':<8} {'المتاحة':<8} {'النسبة':<8}")
                    print("-" * 80)
                    for cat in capacity:
                        print(f"{cat['category']:<25} {cat['current_companies']:<8} {cat['max_companies']:<8} {cat['available_slots']:<8} {cat['usage_percentage']:<8.1f}%")
                else:
                    print("❌ لا توجد بيانات متاحة")
                    
            elif choice == '4':
                health = system_info.get_system_health()
                status_ar = {'healthy': 'سليم', 'warning': 'تحذير', 'critical': 'خطر'}[health['status']]
                print(f"\n🔍 حالة النظام: {status_ar}")
                
                if health['issues']:
                    print("\n❌ مشاكل:")
                    for issue in health['issues']:
                        print(f"   - {issue}")
                
                if health['warnings']:
                    print("\n⚠️ تحذيرات:")
                    for warning in health['warnings']:
                        print(f"   - {warning}")
                
                if not health['issues'] and not health['warnings']:
                    print("✅ النظام يعمل بشكل طبيعي")
                    
            elif choice == '5':
                output_file = system_info.export_stats_json()
                print(f"✅ تم تصدير الإحصائيات إلى: {output_file}")
                
            elif choice == '0':
                print("👋 وداعاً!")
                break
                
            else:
                print("❌ اختيار غير صحيح")
                
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == '__main__':
    main()