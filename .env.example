# إعدادات التطبيق - Company Management System Configuration
# انسخ هذا الملف إلى .env وقم بتعديل القيم حسب الحاجة

# مفتاح الأمان (يجب تغييره في الإنتاج)
SECRET_KEY=your-very-secret-key-change-this-in-production

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///companies.db
# للاستخدام مع PostgreSQL:
# DATABASE_URL=postgresql://username:password@localhost/companies_db
# للاستخدام مع MySQL:
# DATABASE_URL=mysql://username:password@localhost/companies_db

# بيئة التطبيق
FLASK_ENV=development
FLASK_DEBUG=True

# إعدادات رفع الملفات
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216  # 16MB in bytes

# إعدادات البريد الإلكتروني (للإشعارات المستقبلية)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# إعدادات النسخ الاحتياطي
BACKUP_SCHEDULE=daily
BACKUP_RETENTION_DAYS=30
AUTO_BACKUP_ENABLED=True

# إعدادات الأمان
SESSION_TIMEOUT_HOURS=24
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=30

# إعدادات التطبيق
COMPANIES_PER_PAGE=10
DEFAULT_LANGUAGE=ar
TIMEZONE=Asia/Riyadh