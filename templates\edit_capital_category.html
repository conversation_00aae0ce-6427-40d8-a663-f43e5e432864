{% extends "base.html" %}

{% block title %}تعديل فئة رأس المال - {{ category.name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-pencil-square"></i>
        تعديل فئة رأس المال
    </h2>
    <a href="{{ url_for('capital_categories') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i>
        العودة للفئات
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات الفئة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الفئة *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ category.name }}" required>
                            <div class="form-text">اسم وصفي للفئة</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="amount" class="form-label">المبلغ (دينار) *</label>
                            <input type="number" class="form-control" id="amount" name="amount" 
                                   value="{{ category.amount }}" min="1" required>
                            <div class="form-text">مبلغ رأس المال بالدينار</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="max_companies" class="form-label">الحد الأقصى للشركات *</label>
                            <input type="number" class="form-control" id="max_companies" name="max_companies" 
                                   value="{{ category.max_companies }}" min="1" required>
                            <div class="form-text">عدد الشركات المسموح بها في هذه الفئة</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الشركات الحالية</label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-info fs-6">{{ category.current_companies_count }} شركة</span>
                            </div>
                            <div class="form-text">عدد الشركات المسجلة حالياً</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ category.description or '' }}</textarea>
                        <div class="form-text">وصف اختياري للفئة</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i>
                            حفظ التغييرات
                        </button>
                        
                        <a href="{{ url_for('capital_categories') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- معلومات إضافية -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات الفئة
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>تاريخ الإنشاء:</strong>
                    <p class="text-muted">{{ category.created_at.strftime('%Y-%m-%d %H:%M') if category.created_at else 'غير محدد' }}</p>
                </div>
                
                <div class="mb-3">
                    <strong>معدل الإشغال:</strong>
                    {% set usage_percentage = (category.current_companies_count / category.max_companies * 100) if category.max_companies > 0 else 0 %}
                    <div class="progress mb-2" style="height: 20px;">
                        <div class="progress-bar {% if usage_percentage >= 100 %}bg-danger{% elif usage_percentage >= 80 %}bg-warning{% else %}bg-success{% endif %}" 
                             style="width: {{ usage_percentage }}%">
                            {{ "%.0f"|format(usage_percentage) }}%
                        </div>
                    </div>
                    <small class="text-muted">{{ category.current_companies_count }} من {{ category.max_companies }} شركة</small>
                </div>
                
                <div class="mb-3">
                    <strong>الحالة:</strong>
                    <p>
                        {% if category.is_active %}
                        <span class="badge bg-success">نشطة</span>
                        {% else %}
                        <span class="badge bg-danger">غير نشطة</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
        
        <!-- تحذيرات -->
        <div class="card mt-3">
            <div class="card-header bg-warning">
                <h6 class="mb-0">
                    <i class="bi bi-exclamation-triangle"></i>
                    تنبيهات مهمة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        تغيير المبلغ لن يؤثر على الشركات الموجودة
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-exclamation-circle text-warning"></i>
                        تقليل الحد الأقصى قد يمنع إضافة شركات جديدة
                    </li>
                    <li>
                        <i class="bi bi-shield-check text-success"></i>
                        لا يمكن حذف الفئة إذا كانت تحتوي على شركات
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// تحديث اسم الفئة تلقائياً عند تغيير المبلغ
document.getElementById('amount').addEventListener('input', function() {
    const amount = this.value;
    const nameField = document.getElementById('name');
    const currentName = nameField.value;
    
    // فقط إذا كان الاسم يتبع النمط القياسي
    if (currentName.includes('فئة') && currentName.includes('دينار')) {
        if (amount) {
            nameField.value = `فئة ${parseInt(amount).toLocaleString()} دينار`;
        }
    }
});

// تحذير عند تقليل الحد الأقصى
document.getElementById('max_companies').addEventListener('change', function() {
    const newMax = parseInt(this.value);
    const currentCompanies = {{ category.current_companies_count }};
    
    if (newMax < currentCompanies) {
        showAlert('تحذير: الحد الأقصى الجديد أقل من عدد الشركات الحالية (' + currentCompanies + ')', 'warning');
    }
});
</script>
{% endblock %}