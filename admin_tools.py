#!/usr/bin/env python3
"""
أدوات إدارية لنظام إدارة الشركات
Administrative tools for Company Management System
"""

import os
import sys
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
import json
import csv

class AdminTools:
    def __init__(self, app_root=None):
        self.app_root = Path(app_root) if app_root else Path(__file__).parent
        self.db_path = self.app_root / 'companies.db'
    
    def create_user(self, username, email, password, is_admin=False):
        """إنشاء مستخدم جديد"""
        try:
            from app import create_app, db
            from models import User
            
            app = create_app()
            with app.app_context():
                # التحقق من عدم وجود المستخدم
                existing_user = User.query.filter_by(username=username).first()
                if existing_user:
                    print(f"❌ المستخدم '{username}' موجود مسبقاً")
                    return False
                
                # إنشاء المستخدم الجديد
                user = User(
                    username=username,
                    email=email,
                    is_admin=is_admin
                )
                user.set_password(password)
                
                db.session.add(user)
                db.session.commit()
                
                user_type = "مدير" if is_admin else "مستخدم عادي"
                print(f"✅ تم إنشاء {user_type}: {username}")
                return True
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء المستخدم: {e}")
            return False
    
    def reset_user_password(self, username, new_password):
        """إعادة تعيين كلمة مرور المستخدم"""
        try:
            from app import create_app, db
            from models import User
            
            app = create_app()
            with app.app_context():
                user = User.query.filter_by(username=username).first()
                if not user:
                    print(f"❌ المستخدم '{username}' غير موجود")
                    return False
                
                user.set_password(new_password)
                db.session.commit()
                
                print(f"✅ تم تغيير كلمة مرور المستخدم: {username}")
                return True
                
        except Exception as e:
            print(f"❌ خطأ في تغيير كلمة المرور: {e}")
            return False
    
    def list_users(self):
        """عرض قائمة المستخدمين"""
        try:
            from app import create_app
            from models import User
            
            app = create_app()
            with app.app_context():
                users = User.query.all()
                
                if not users:
                    print("لا يوجد مستخدمين في النظام")
                    return
                
                print("📋 قائمة المستخدمين:")
                print("-" * 60)
                print(f"{'الرقم':<5} {'اسم المستخدم':<15} {'البريد الإلكتروني':<25} {'النوع':<10} {'تاريخ الإنشاء'}")
                print("-" * 60)
                
                for i, user in enumerate(users, 1):
                    user_type = "مدير" if user.is_admin else "عادي"
                    created_date = user.created_at.strftime('%Y-%m-%d')
                    print(f"{i:<5} {user.username:<15} {user.email:<25} {user_type:<10} {created_date}")
                
        except Exception as e:
            print(f"❌ خطأ في عرض المستخدمين: {e}")
    
    def export_data(self, table_name=None, output_format='csv'):
        """تصدير البيانات"""
        if not self.db_path.exists():
            print("❌ قاعدة البيانات غير موجودة")
            return False
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # الحصول على أسماء الجداول
        if table_name:
            tables = [table_name]
        else:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in cursor.fetchall()]
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        export_dir = self.app_root / 'exports'
        export_dir.mkdir(exist_ok=True)
        
        for table in tables:
            try:
                cursor.execute(f"SELECT * FROM {table}")
                rows = cursor.fetchall()
                
                if not rows:
                    print(f"⚠️  الجدول '{table}' فارغ")
                    continue
                
                # الحصول على أسماء الأعمدة
                cursor.execute(f"PRAGMA table_info({table})")
                columns = [col[1] for col in cursor.fetchall()]
                
                if output_format.lower() == 'csv':
                    output_file = export_dir / f'{table}_{timestamp}.csv'
                    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                        writer = csv.writer(csvfile)
                        writer.writerow(columns)
                        writer.writerows(rows)
                
                elif output_format.lower() == 'json':
                    output_file = export_dir / f'{table}_{timestamp}.json'
                    data = []
                    for row in rows:
                        data.append(dict(zip(columns, row)))
                    
                    with open(output_file, 'w', encoding='utf-8') as jsonfile:
                        json.dump(data, jsonfile, ensure_ascii=False, indent=2, default=str)
                
                print(f"✅ تم تصدير جدول '{table}' إلى: {output_file}")
                
            except Exception as e:
                print(f"❌ خطأ في تصدير جدول '{table}': {e}")
        
        conn.close()
        return True
    
    def cleanup_database(self):
        """تنظيف قاعدة البيانات"""
        try:
            from app import create_app, db
            from models import Company, CompanyFile, Person
            
            app = create_app()
            with app.app_context():
                cleanup_count = 0
                
                # حذف الشركات غير النشطة القديمة (أكثر من 6 أشهر)
                six_months_ago = datetime.now() - timedelta(days=180)
                old_companies = Company.query.filter(
                    Company.is_active == False,
                    Company.updated_at < six_months_ago
                ).all()
                
                for company in old_companies:
                    # حذف الملفات المرتبطة
                    for file in company.files:
                        file_path = Path(file.file_path)
                        if file_path.exists():
                            file_path.unlink()
                    
                    db.session.delete(company)
                    cleanup_count += 1
                
                # حذف الأشخاص غير المرتبطين بأي شركة
                orphaned_people = Person.query.filter(
                    ~Person.id.in_(
                        db.session.query(Company.primary_owner_id).distinct()
                    )
                ).all()
                
                for person in orphaned_people:
                    if not person.company_ownerships:  # لا يملك أي شركات
                        db.session.delete(person)
                        cleanup_count += 1
                
                db.session.commit()
                print(f"✅ تم تنظيف قاعدة البيانات ({cleanup_count} عنصر محذوف)")
                return True
                
        except Exception as e:
            print(f"❌ خطأ في تنظيف قاعدة البيانات: {e}")
            return False
    
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        if not self.db_path.exists():
            print("❌ قاعدة البيانات غير موجودة")
            return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # تحسين قاعدة البيانات
            conn.execute("VACUUM")
            conn.execute("ANALYZE")
            
            conn.close()
            print("✅ تم تحسين قاعدة البيانات")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحسين قاعدة البيانات: {e}")
            return False
    
    def generate_report(self, report_type='summary'):
        """إنشاء تقارير"""
        try:
            from system_info import SystemInfo
            
            system_info = SystemInfo(self.app_root)
            
            if report_type == 'summary':
                system_info.print_summary_report()
            elif report_type == 'detailed':
                # تقرير مفصل
                stats = system_info.get_database_stats()
                health = system_info.get_system_health()
                capacity = system_info.get_capacity_analysis()
                
                # حفظ التقرير في ملف
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                report_file = self.app_root / f'detailed_report_{timestamp}.txt'
                
                with open(report_file, 'w', encoding='utf-8') as f:
                    f.write("=" * 60 + "\n")
                    f.write("تقرير مفصل - نظام إدارة الشركات\n")
                    f.write("=" * 60 + "\n")
                    f.write(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    
                    # إحصائيات قاعدة البيانات
                    if stats:
                        f.write("إحصائيات قاعدة البيانات:\n")
                        f.write("-" * 30 + "\n")
                        for key, value in stats.items():
                            f.write(f"{key}: {value}\n")
                        f.write("\n")
                    
                    # تحليل السعة
                    if capacity:
                        f.write("تحليل السعة:\n")
                        f.write("-" * 30 + "\n")
                        for cat in capacity:
                            f.write(f"{cat['category']}: {cat['current_companies']}/{cat['max_companies']} ({cat['usage_percentage']:.1f}%)\n")
                        f.write("\n")
                    
                    # صحة النظام
                    f.write("صحة النظام:\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"الحالة: {health['status']}\n")
                    
                    if health['issues']:
                        f.write("المشاكل:\n")
                        for issue in health['issues']:
                            f.write(f"  - {issue}\n")
                    
                    if health['warnings']:
                        f.write("التحذيرات:\n")
                        for warning in health['warnings']:
                            f.write(f"  - {warning}\n")
                
                print(f"✅ تم إنشاء التقرير المفصل: {report_file}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء التقرير: {e}")
            return False

def main():
    """الواجهة التفاعلية للأدوات الإدارية"""
    admin_tools = AdminTools()
    
    while True:
        print("\n" + "=" * 50)
        print("🔧 الأدوات الإدارية - نظام إدارة الشركات")
        print("=" * 50)
        print("1. إدارة المستخدمين")
        print("2. تصدير البيانات")
        print("3. تنظيف قاعدة البيانات")
        print("4. تحسين قاعدة البيانات")
        print("5. إنشاء تقارير")
        print("0. خروج")
        print("-" * 50)
        
        choice = input("اختر العملية المطلوبة: ").strip()
        
        try:
            if choice == '1':
                # إدارة المستخدمين
                print("\n👥 إدارة المستخدمين:")
                print("1. عرض المستخدمين")
                print("2. إنشاء مستخدم جديد")
                print("3. إعادة تعيين كلمة المرور")
                
                user_choice = input("اختر: ").strip()
                
                if user_choice == '1':
                    admin_tools.list_users()
                elif user_choice == '2':
                    username = input("اسم المستخدم: ").strip()
                    email = input("البريد الإلكتروني: ").strip()
                    password = input("كلمة المرور: ").strip()
                    is_admin = input("هل هو مدير؟ (y/n): ").lower() == 'y'
                    admin_tools.create_user(username, email, password, is_admin)
                elif user_choice == '3':
                    username = input("اسم المستخدم: ").strip()
                    new_password = input("كلمة المرور الجديدة: ").strip()
                    admin_tools.reset_user_password(username, new_password)
                    
            elif choice == '2':
                # تصدير البيانات
                table_name = input("اسم الجدول (اتركه فارغاً لتصدير الكل): ").strip()
                output_format = input("نوع التصدير (csv/json): ").strip() or 'csv'
                admin_tools.export_data(table_name if table_name else None, output_format)
                
            elif choice == '3':
                # تنظيف قاعدة البيانات
                confirm = input("هل أنت متأكد من تنظيف قاعدة البيانات؟ (y/n): ").lower()
                if confirm == 'y':
                    admin_tools.cleanup_database()
                    
            elif choice == '4':
                # تحسين قاعدة البيانات
                admin_tools.optimize_database()
                
            elif choice == '5':
                # إنشاء تقارير
                print("\n📊 أنواع التقارير:")
                print("1. تقرير ملخص")
                print("2. تقرير مفصل")
                
                report_choice = input("اختر نوع التقرير: ").strip()
                
                if report_choice == '1':
                    admin_tools.generate_report('summary')
                elif report_choice == '2':
                    admin_tools.generate_report('detailed')
                    
            elif choice == '0':
                print("👋 وداعاً!")
                break
                
            else:
                print("❌ اختيار غير صحيح")
                
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == '__main__':
    main()