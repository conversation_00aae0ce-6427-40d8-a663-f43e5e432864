{% extends "base.html" %}

{% block title %}{{ company.name }} - تفاصيل الشركة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 fade-in-up">
    <div>
        <h2>
            <i class="bi bi-building text-primary me-2"></i>
            {{ company.name }}
        </h2>
        <div class="d-flex align-items-center gap-2 mt-2">
            <span class="badge bg-primary fs-6">
                <i class="bi bi-cash me-1"></i>
                {{ company.capital_category.name }}
            </span>
            <span class="badge bg-success fs-6">
                <i class="bi bi-check-circle me-1"></i>
                نشطة
            </span>
            <small class="text-muted">
                <i class="bi bi-calendar3 me-1"></i>
                تاريخ التسجيل: {{ company.created_at.strftime('%Y-%m-%d') }}
            </small>
        </div>
    </div>
    <div class="action-buttons">
        <a href="{{ url_for('category_companies', category_id=company.capital_category_id) }}" 
           class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للشركات
        </a>
        <a href="{{ url_for('edit_company', company_id=company.id) }}" class="btn btn-warning">
            <i class="bi bi-pencil-square me-2"></i>
            تعديل البيانات
        </a>
        <button class="btn btn-outline-danger" 
                onclick="confirmDeleteCompany('{{ company.name }}', '{{ url_for('delete_company', company_id=company.id) }}')">
            <i class="bi bi-trash3 me-2"></i>
            حذف الشركة
        </button>
    </div>
</div>

<div class="row">
    <!-- Company Information -->
    <div class="col-md-8">
        <div class="card mb-4 slide-in-right" style="animation-delay: 0.2s;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle text-info me-2"></i>
                    معلومات الشركة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>اسم الشركة:</strong>
                        <p class="text-muted">{{ company.name }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>فئة رأس المال:</strong>
                        <p class="text-muted">{{ company.capital_category.name }} ({{ "{:,}".format(company.capital_category.amount) }} دينار)</p>
                    </div>
                </div>
                
                <div class="mb-3">
                    <strong>خطة المشروع:</strong>
                    <p class="text-muted">{{ company.project_plan }}</p>
                </div>
                
                {% if company.resources %}
                <div class="mb-3">
                    <strong>الموارد:</strong>
                    <p class="text-muted">{{ company.resources }}</p>
                </div>
                {% endif %}
                
                {% if company.work_method %}
                <div class="mb-3">
                    <strong>طريقة العمل:</strong>
                    <p class="text-muted">{{ company.work_method }}</p>
                </div>
                {% endif %}
                
                <div class="row">
                    <div class="col-md-6">
                        <strong>تاريخ الإنشاء:</strong>
                        <p class="text-muted">{{ company.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                    <div class="col-md-6">
                        <strong>آخر تحديث:</strong>
                        <p class="text-muted">{{ company.updated_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ownership Information -->
        <div class="card mb-4 slide-in-right" style="animation-delay: 0.4s;">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-people text-success me-2"></i>
                    معلومات الملكية
                </h5>
                <button class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#addOwnerModal">
                    <i class="bi bi-person-plus me-1"></i>
                    إضافة مالك
                </button>
            </div>
            <div class="card-body">
                <!-- Primary Owner -->
                <div class="d-flex align-items-center mb-3 p-3 bg-light rounded border-start border-primary border-4">
                    <div class="me-3">
                        <div class="avatar-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; border-radius: 50%; font-weight: bold; font-size: 1.2rem;">
                            {{ company.primary_owner.name[0].upper() }}
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1 fw-bold">
                            {{ company.primary_owner.name }} 
                            <span class="badge bg-primary ms-2">
                                <i class="bi bi-star-fill me-1"></i>
                                مالك أساسي
                            </span>
                        </h6>
                        <div class="row">
                            {% if company.primary_owner.national_id %}
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="bi bi-card-text me-1"></i> 
                                    {{ company.primary_owner.national_id }}
                                </small>
                            </div>
                            {% endif %}
                            {% if company.primary_owner.phone %}
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="bi bi-telephone me-1"></i> 
                                    {{ company.primary_owner.phone }}
                                </small>
                            </div>
                            {% endif %}
                        </div>
                        {% if company.primary_owner.email %}
                        <div class="mt-1">
                            <small class="text-muted">
                                <i class="bi bi-envelope me-1"></i> 
                                {{ company.primary_owner.email }}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Additional Owners -->
                {% if company.additional_owners %}
                <h6 class="text-muted mb-3">الملاك الإضافيون:</h6>
                {% for ownership in company.additional_owners %}
                <div class="d-flex align-items-center mb-2 p-2 border rounded">
                    <div class="me-3">
                        <i class="bi bi-person text-info"></i>
                    </div>
                    <div class="flex-grow-1">
                        <strong>{{ ownership.person.name }}</strong>
                        {% if ownership.role %}
                        <span class="badge bg-info">{{ ownership.role }}</span>
                        {% endif %}
                        {% if ownership.ownership_percentage > 0 %}
                        <span class="text-muted">({{ ownership.ownership_percentage }}%)</span>
                        {% endif %}
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-danger">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <p class="text-muted text-center py-3">لا يوجد ملاك إضافيون</p>
                {% endif %}
            </div>
        </div>

        <!-- Files Section -->
        <div class="card slide-in-right" style="animation-delay: 0.6s;">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-files text-warning me-2"></i>
                    الملفات والمستندات 
                    <span class="badge bg-warning text-dark ms-2">{{ company.files|length }}</span>
                </h5>
                <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                    <i class="bi bi-cloud-upload me-1"></i>
                    رفع ملف
                </button>
            </div>
            <div class="card-body">
                {% if company.files %}
                <div class="row">
                    {% for file in company.files %}
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body p-3">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        {% if file.file_type == 'image' %}
                                        <i class="bi bi-image text-success display-6"></i>
                                        {% else %}
                                        <i class="bi bi-file-earmark text-primary display-6"></i>
                                        {% endif %}
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ file.original_filename }}</h6>
                                        <p class="mb-1 text-muted small">{{ file.description or 'لا يوجد وصف' }}</p>
                                        <small class="text-muted">
                                            {{ (file.file_size / 1024 / 1024)|round(2) }} MB - 
                                            {{ file.uploaded_at.strftime('%Y-%m-%d') }}
                                        </small>
                                    </div>
                                    <div>
                                        <button class="btn btn-sm btn-outline-info me-1">
                                            <i class="bi bi-download"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-files display-4 text-muted"></i>
                    <p class="text-muted mt-2">لا توجد ملفات مرفوعة</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="bi bi-cloud-upload"></i>
                        رفع أول ملف
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <div class="card mb-4 slide-in-left" style="animation-delay: 0.3s;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up text-success me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h4 class="text-primary">{{ company.files|length }}</h4>
                        <small class="text-muted">ملف مرفوع</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success">{{ company.additional_owners|length + 1 }}</h4>
                        <small class="text-muted">مالك</small>
                    </div>
                </div>
                <div class="text-center">
                    <h4 class="text-info">{{ (company.created_at - company.created_at).days or 0 }}</h4>
                    <small class="text-muted">يوم منذ الإنشاء</small>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('edit_company', company_id=company.id) }}" class="btn btn-outline-primary">
                        <i class="bi bi-pencil"></i>
                        تعديل البيانات
                    </a>
                    <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#addOwnerModal">
                        <i class="bi bi-person-plus"></i>
                        إضافة مالك جديد
                    </button>
                    <button class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="bi bi-cloud-upload"></i>
                        رفع ملف جديد
                    </button>
                    <hr>
                    <button class="btn btn-outline-warning">
                        <i class="bi bi-archive"></i>
                        أرشفة الشركة
                    </button>
                    <button class="btn btn-outline-danger">
                        <i class="bi bi-trash"></i>
                        حذف الشركة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفع ملف جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('upload_company_file', company_id=company.id) }}" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="file" class="form-label">اختر الملف</label>
                        <input type="file" class="form-control" id="file" name="file" required>
                        <div class="form-text">الأنواع المدعومة: PDF, DOC, DOCX, JPG, PNG, GIF</div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الملف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف اختياري للملف..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">رفع الملف</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Owner Modal -->
<div class="modal fade" id="addOwnerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مالك جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">هذه الميزة ستكون متاحة قريباً</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// دالة تأكيد حذف الشركة
function confirmDeleteCompany(companyName, deleteUrl) {
    if (confirm(`هل أنت متأكد من حذف الشركة "${companyName}"؟\n\nسيتم إلغاء تفعيل الشركة وإخفاؤها من القوائم.`)) {
        // إنشاء نموذج مخفي للحذف
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;
        document.body.appendChild(form);
        form.submit();
    }
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للكروت
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // تحسين نموذج رفع الملفات
    const fileInput = document.getElementById('file');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            const fileName = this.files[0]?.name;
            if (fileName) {
                const descriptionField = document.getElementById('description');
                if (!descriptionField.value) {
                    descriptionField.placeholder = `ملف: ${fileName}`;
                }
            }
        });
    }
});

// دالة عرض التنبيهات
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}