#!/usr/bin/env python3
"""
مدير النسخ الاحتياطية
Backup Manager for Company Management System
"""

import os
import shutil
import sqlite3
import zipfile
from datetime import datetime
from pathlib import Path

class BackupManager:
    def __init__(self, app_root=None):
        self.app_root = Path(app_root) if app_root else Path(__file__).parent
        self.backup_dir = self.app_root / 'backups'
        self.backup_dir.mkdir(exist_ok=True)
        
    def create_backup(self, include_files=True):
        """إنشاء نسخة احتياطية شاملة"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'company_system_backup_{timestamp}'
        backup_path = self.backup_dir / f'{backup_name}.zip'
        
        print(f"🔄 إنشاء نسخة احتياطية: {backup_name}")
        
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # نسخ قاعدة البيانات
            db_path = self.app_root / 'companies.db'
            if db_path.exists():
                zipf.write(db_path, 'companies.db')
                print("✅ تم نسخ قاعدة البيانات")
            
            # نسخ الملفات المرفوعة
            if include_files:
                uploads_dir = self.app_root / 'uploads'
                if uploads_dir.exists():
                    for file_path in uploads_dir.rglob('*'):
                        if file_path.is_file():
                            arcname = file_path.relative_to(self.app_root)
                            zipf.write(file_path, arcname)
                    print("✅ تم نسخ الملفات المرفوعة")
            
            # نسخ ملفات التكوين
            config_files = ['config.py', 'requirements.txt', 'README.md']
            for config_file in config_files:
                file_path = self.app_root / config_file
                if file_path.exists():
                    zipf.write(file_path, config_file)
            
            print("✅ تم نسخ ملفات التكوين")
        
        backup_size = backup_path.stat().st_size / (1024 * 1024)  # MB
        print(f"✅ تم إنشاء النسخة الاحتياطية بنجاح!")
        print(f"📁 المسار: {backup_path}")
        print(f"📊 الحجم: {backup_size:.2f} MB")
        
        return backup_path
    
    def restore_backup(self, backup_path):
        """استعادة نسخة احتياطية"""
        backup_path = Path(backup_path)
        if not backup_path.exists():
            raise FileNotFoundError(f"ملف النسخة الاحتياطية غير موجود: {backup_path}")
        
        print(f"🔄 استعادة النسخة الاحتياطية: {backup_path.name}")
        
        # إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
        current_backup = self.create_backup()
        print(f"💾 تم حفظ النسخة الحالية في: {current_backup}")
        
        with zipfile.ZipFile(backup_path, 'r') as zipf:
            # استعادة قاعدة البيانات
            if 'companies.db' in zipf.namelist():
                zipf.extract('companies.db', self.app_root)
                print("✅ تم استعادة قاعدة البيانات")
            
            # استعادة الملفات المرفوعة
            for file_info in zipf.filelist:
                if file_info.filename.startswith('uploads/'):
                    zipf.extract(file_info, self.app_root)
            print("✅ تم استعادة الملفات المرفوعة")
        
        print("✅ تم استعادة النسخة الاحتياطية بنجاح!")
    
    def list_backups(self):
        """عرض قائمة النسخ الاحتياطية"""
        backups = list(self.backup_dir.glob('*.zip'))
        backups.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        if not backups:
            print("📂 لا توجد نسخ احتياطية")
            return []
        
        print("📋 النسخ الاحتياطية المتاحة:")
        print("-" * 80)
        
        backup_info = []
        for i, backup in enumerate(backups, 1):
            stat = backup.stat()
            size_mb = stat.st_size / (1024 * 1024)
            created = datetime.fromtimestamp(stat.st_mtime)
            
            info = {
                'index': i,
                'name': backup.name,
                'path': backup,
                'size_mb': size_mb,
                'created': created
            }
            backup_info.append(info)
            
            print(f"{i:2d}. {backup.name}")
            print(f"    📅 تاريخ الإنشاء: {created.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"    📊 الحجم: {size_mb:.2f} MB")
            print()
        
        return backup_info
    
    def delete_backup(self, backup_path):
        """حذف نسخة احتياطية"""
        backup_path = Path(backup_path)
        if backup_path.exists():
            backup_path.unlink()
            print(f"🗑️ تم حذف النسخة الاحتياطية: {backup_path.name}")
        else:
            print(f"❌ النسخة الاحتياطية غير موجودة: {backup_path}")
    
    def cleanup_old_backups(self, keep_count=10):
        """حذف النسخ الاحتياطية القديمة والاحتفاظ بعدد محدد"""
        backups = list(self.backup_dir.glob('*.zip'))
        backups.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        if len(backups) > keep_count:
            old_backups = backups[keep_count:]
            for backup in old_backups:
                self.delete_backup(backup)
            print(f"🧹 تم حذف {len(old_backups)} نسخة احتياطية قديمة")
        else:
            print("✅ لا توجد نسخ احتياطية قديمة للحذف")
    
    def get_database_info(self):
        """الحصول على معلومات قاعدة البيانات"""
        db_path = self.app_root / 'companies.db'
        if not db_path.exists():
            return None
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحصول على أسماء الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        info = {
            'file_size_mb': db_path.stat().st_size / (1024 * 1024),
            'tables': []
        }
        
        for table_name in tables:
            table_name = table_name[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            info['tables'].append({'name': table_name, 'count': count})
        
        conn.close()
        return info

def main():
    """الواجهة التفاعلية لإدارة النسخ الاحتياطية"""
    backup_manager = BackupManager()
    
    while True:
        print("\n" + "=" * 50)
        print("🏢 مدير النسخ الاحتياطية - نظام إدارة الشركات")
        print("=" * 50)
        print("1. إنشاء نسخة احتياطية جديدة")
        print("2. عرض النسخ الاحتياطية المتاحة")
        print("3. استعادة نسخة احتياطية")
        print("4. حذف نسخة احتياطية")
        print("5. تنظيف النسخ القديمة")
        print("6. معلومات قاعدة البيانات")
        print("0. خروج")
        print("-" * 50)
        
        choice = input("اختر العملية المطلوبة: ").strip()
        
        try:
            if choice == '1':
                include_files = input("هل تريد تضمين الملفات المرفوعة؟ (y/n): ").lower() == 'y'
                backup_manager.create_backup(include_files)
                
            elif choice == '2':
                backup_manager.list_backups()
                
            elif choice == '3':
                backups = backup_manager.list_backups()
                if backups:
                    try:
                        index = int(input("أدخل رقم النسخة الاحتياطية للاستعادة: ")) - 1
                        if 0 <= index < len(backups):
                            confirm = input("هل أنت متأكد من الاستعادة؟ (y/n): ").lower()
                            if confirm == 'y':
                                backup_manager.restore_backup(backups[index]['path'])
                        else:
                            print("❌ رقم غير صحيح")
                    except ValueError:
                        print("❌ يرجى إدخال رقم صحيح")
                        
            elif choice == '4':
                backups = backup_manager.list_backups()
                if backups:
                    try:
                        index = int(input("أدخل رقم النسخة الاحتياطية للحذف: ")) - 1
                        if 0 <= index < len(backups):
                            confirm = input("هل أنت متأكد من الحذف؟ (y/n): ").lower()
                            if confirm == 'y':
                                backup_manager.delete_backup(backups[index]['path'])
                        else:
                            print("❌ رقم غير صحيح")
                    except ValueError:
                        print("❌ يرجى إدخال رقم صحيح")
                        
            elif choice == '5':
                try:
                    keep_count = int(input("كم نسخة احتياطية تريد الاحتفاظ بها؟ (افتراضي: 10): ") or "10")
                    backup_manager.cleanup_old_backups(keep_count)
                except ValueError:
                    print("❌ يرجى إدخال رقم صحيح")
                    
            elif choice == '6':
                info = backup_manager.get_database_info()
                if info:
                    print(f"\n📊 معلومات قاعدة البيانات:")
                    print(f"📁 حجم الملف: {info['file_size_mb']:.2f} MB")
                    print(f"📋 الجداول:")
                    for table in info['tables']:
                        print(f"   - {table['name']}: {table['count']} سجل")
                else:
                    print("❌ قاعدة البيانات غير موجودة")
                    
            elif choice == '0':
                print("👋 وداعاً!")
                break
                
            else:
                print("❌ اختيار غير صحيح")
                
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == '__main__':
    main()