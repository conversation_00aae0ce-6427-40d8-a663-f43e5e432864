<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الشركات - ليبيا{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    <style>
        :root {
            --bs-body-font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --sidebar-bg: #343a40;
            --sidebar-text: #adb5bd;
            --sidebar-hover: rgba(255, 255, 255, 0.1);
        }

        [data-bs-theme="dark"] {
            --sidebar-bg: #1a1d20;
            --sidebar-text: #6c757d;
            --sidebar-hover: rgba(255, 255, 255, 0.05);
        }

        body {
            font-family: var(--bs-body-font-family);
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        .navbar-brand {
            font-weight: bold;
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid var(--bs-border-color);
            transition: all 0.3s ease;
        }
        
        [data-bs-theme="dark"] .card {
            box-shadow: 0 0.125rem 0.25rem rgba(255, 255, 255, 0.05);
        }
        
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: var(--sidebar-bg);
            transition: background-color 0.3s ease;
        }
        
        .sidebar .nav-link {
            color: var(--sidebar-text);
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover {
            color: #fff;
            background-color: var(--sidebar-hover);
        }
        
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #0d6efd;
        }

        .theme-toggle {
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .theme-toggle:hover {
            transform: scale(1.1);
        }

        /* تحسينات البحث */
        .search-container {
            position: relative;
        }
        
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--bs-body-bg);
            border: 1px solid var(--bs-border-color);
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
        }

        /* تحسينات الجداول */
        .table-responsive {
            border-radius: 0.375rem;
            overflow: hidden;
        }
        
        .table th {
            background-color: var(--bs-primary);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .table tbody tr:hover {
            background-color: var(--bs-light);
        }
        
        [data-bs-theme="dark"] .table tbody tr:hover {
            background-color: var(--bs-dark);
        }

        /* أزرار الإجراءات */
        .action-buttons {
            display: flex;
            gap: 0.25rem;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        /* تحسينات متقدمة للتصميم */
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --card-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --card-shadow-hover: 0 8px 30px rgba(0,0,0,0.12);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* تحسينات الخلفية */
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        [data-bs-theme="dark"] body {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
        }

        /* تحسينات شريط التنقل */
        .navbar {
            backdrop-filter: blur(10px);
            background: rgba(13, 110, 253, 0.95) !important;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        [data-bs-theme="dark"] .navbar {
            background: rgba(45, 55, 72, 0.95) !important;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.3rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        /* تحسينات الكروت */
        .card {
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            border: none;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            transition: var(--transition);
            overflow: hidden;
        }

        [data-bs-theme="dark"] .card {
            background: rgba(45, 55, 72, 0.95);
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--card-shadow-hover);
        }

        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid rgba(0,0,0,0.05);
            font-weight: 600;
            padding: 1.25rem 1.5rem;
        }

        [data-bs-theme="dark"] .card-header {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        /* كروت الإحصائيات المحسنة */
        .stats-card {
            background: var(--primary-gradient);
            color: white;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .stats-card .card-body {
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .stats-card-success {
            background: var(--success-gradient);
        }

        .stats-card-warning {
            background: var(--warning-gradient);
        }

        .stats-card-info {
            background: var(--info-gradient);
        }

        /* تحسينات الأزرار */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: var(--success-gradient);
            border: none;
            box-shadow: 0 4px 15px rgba(17, 153, 142, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(17, 153, 142, 0.4);
        }

        /* تحسينات النماذج */
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            transition: var(--transition);
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            transform: translateY(-1px);
        }

        [data-bs-theme="dark"] .form-control,
        [data-bs-theme="dark"] .form-select {
            background: rgba(45, 55, 72, 0.9);
            border-color: #4a5568;
            color: white;
        }

        /* تحسينات التقدم */
        .progress {
            border-radius: var(--border-radius);
            height: 12px;
            background: rgba(0,0,0,0.05);
            overflow: hidden;
        }

        .progress-bar {
            transition: width 1s ease-in-out;
            position: relative;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background-image: linear-gradient(
                -45deg,
                rgba(255, 255, 255, .2) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, .2) 50%,
                rgba(255, 255, 255, .2) 75%,
                transparent 75%,
                transparent
            );
            background-size: 1rem 1rem;
            animation: progress-bar-stripes 1s linear infinite;
        }

        @keyframes progress-bar-stripes {
            0% { background-position: 1rem 0; }
            100% { background-position: 0 0; }
        }

        /* تحسينات التنبيهات */
        .alert {
            border-radius: var(--border-radius);
            border: none;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: currentColor;
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(17, 153, 142, 0.1) 0%, rgba(56, 239, 125, 0.1) 100%);
            color: #0f766e;
            border-left: 4px solid #11998e;
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(248, 113, 113, 0.1) 100%);
            color: #dc2626;
            border-left: 4px solid #ef4444;
        }

        .alert-warning {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%);
            color: #d97706;
            border-left: 4px solid #f59e0b;
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
            color: #2563eb;
            border-left: 4px solid #3b82f6;
        }

        /* تحسينات الشارات */
        .badge {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
        }

        /* تحسينات النوافذ المنبثقة */
        .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .modal-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid rgba(0,0,0,0.05);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        [data-bs-theme="dark"] .modal-content {
            background: rgba(45, 55, 72, 0.95);
        }

        [data-bs-theme="dark"] .modal-header {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        /* تحسينات الرسوم المتحركة */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .slide-in-right {
            animation: slideInRight 0.6s ease-out;
        }

        /* تحسينات زر تبديل الوضع */
        .theme-toggle:hover {
            transform: rotate(180deg) scale(1.1);
        }

        /* تحسينات التمرير */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.05);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        [data-bs-theme="dark"] ::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.05);
        }

        /* تحسينات الاستجابة */
        @media (max-width: 576px) {
            .action-buttons {
                flex-direction: column;
                gap: 0.25rem;
            }

            .stats-card h3 {
                font-size: 2rem;
            }

            .card-body {
                padding: 1rem;
            }

            .container-fluid {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') if current_user.is_authenticated else url_for('index') }}">
                <i class="bi bi-building-gear me-2"></i>
                نظام إدارة الشركات - ليبيا
                <small class="d-block fs-6 opacity-75">Company Management System</small>
            </a>
            
            {% if current_user.is_authenticated %}
            <div class="navbar-nav ms-auto d-flex align-items-center">
                <!-- زر البحث السريع -->
                <div class="nav-item me-3">
                    <div class="search-container">
                        <input type="text" class="form-control form-control-sm" id="globalSearch" 
                               placeholder="بحث سريع..." style="width: 200px;">
                        <div id="searchResults" class="search-results d-none"></div>
                    </div>
                </div>
                
                <!-- زر تبديل الوضع المظلم -->
                <div class="nav-item me-3">
                    <button class="btn btn-outline-light btn-sm theme-toggle" id="themeToggle" title="تبديل الوضع المظلم/الفاتح">
                        <i class="bi bi-moon-fill" id="themeIcon"></i>
                        <span class="d-none d-md-inline ms-1" id="themeText">مظلم</span>
                    </button>
                </div>
                
                <!-- قائمة المستخدم -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle"></i>
                        {{ current_user.username }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">
                            <i class="bi bi-person"></i> الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="bi bi-gear"></i> الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                            <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
            {% else %}
            <!-- زر تبديل الوضع للمستخدمين غير المسجلين -->
            <div class="navbar-nav ms-auto">
                <div class="nav-item">
                    <button class="btn btn-outline-light btn-sm theme-toggle" id="themeToggle" title="تبديل الوضع">
                        <i class="bi bi-moon-fill" id="themeIcon"></i>
                    </button>
                </div>
            </div>
            {% endif %}
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container-fluid mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Container -->
    <div class="container-fluid">
        <div class="row">
            {% if current_user.is_authenticated %}
            <!-- Sidebar -->
            <div class="col-md-2 p-0">
                <div class="sidebar">
                    <nav class="nav flex-column p-3">
                        <small class="text-muted px-3 mb-2">القائمة الرئيسية</small>
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="bi bi-speedometer2"></i> لوحة التحكم
                        </a>
                        <a class="nav-link" href="{{ url_for('capital_categories') }}">
                            <i class="bi bi-cash-stack"></i> فئات رأس المال
                        </a>
                        <a class="nav-link" href="{{ url_for('people') }}">
                            <i class="bi bi-people"></i> إدارة الأشخاص
                        </a>
                        <hr class="text-light">
                        <small class="text-muted px-3">الإعدادات</small>
                        <a class="nav-link" href="#">
                            <i class="bi bi-gear"></i> الإعدادات العامة
                        </a>
                        <a class="nav-link" href="#">
                            <i class="bi bi-download"></i> النسخ الاحتياطي
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content Area with Sidebar -->
            <div class="col-md-10">
            {% else %}
            <!-- Full width for non-authenticated users -->
            <div class="col-12">
            {% endif %}
                <div class="p-4">
                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // إدارة الوضع المظلم
        class ThemeManager {
            constructor() {
                this.themeToggle = document.getElementById('themeToggle');
                this.themeIcon = document.getElementById('themeIcon');
                this.themeText = document.getElementById('themeText');
                this.init();
            }

            init() {
                // تحميل الوضع المحفوظ
                const savedTheme = localStorage.getItem('theme') || 'light';
                this.setTheme(savedTheme);
                
                // إضافة مستمع الأحداث
                if (this.themeToggle) {
                    this.themeToggle.addEventListener('click', () => this.toggleTheme());
                }
            }

            setTheme(theme) {
                document.documentElement.setAttribute('data-bs-theme', theme);
                localStorage.setItem('theme', theme);
                
                if (this.themeIcon && this.themeText) {
                    if (theme === 'dark') {
                        this.themeIcon.className = 'bi bi-sun-fill';
                        this.themeText.textContent = 'فاتح';
                        this.themeToggle.title = 'تبديل للوضع الفاتح';
                    } else {
                        this.themeIcon.className = 'bi bi-moon-fill';
                        this.themeText.textContent = 'مظلم';
                        this.themeToggle.title = 'تبديل للوضع المظلم';
                    }
                }
            }

            toggleTheme() {
                const currentTheme = document.documentElement.getAttribute('data-bs-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                this.setTheme(newTheme);
                
                // تأثير بصري للتبديل
                document.body.style.transition = 'all 0.3s ease';
                setTimeout(() => {
                    document.body.style.transition = '';
                }, 300);
            }
        }

        // إدارة البحث السريع
        class SearchManager {
            constructor() {
                this.searchInput = document.getElementById('globalSearch');
                this.searchResults = document.getElementById('searchResults');
                this.init();
            }

            init() {
                if (this.searchInput) {
                    this.searchInput.addEventListener('input', (e) => this.handleSearch(e));
                    this.searchInput.addEventListener('focus', () => this.showResults());
                    document.addEventListener('click', (e) => this.handleClickOutside(e));
                }
            }

            async handleSearch(e) {
                const query = e.target.value.trim();
                
                if (query.length < 2) {
                    this.hideResults();
                    return;
                }

                try {
                    const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
                    const data = await response.json();
                    this.displayResults(data);
                } catch (error) {
                    console.error('خطأ في البحث:', error);
                }
            }

            displayResults(results) {
                if (!results || results.length === 0) {
                    this.searchResults.innerHTML = '<div class="p-2 text-muted">لا توجد نتائج</div>';
                } else {
                    const html = results.map(item => `
                        <div class="p-2 border-bottom">
                            <a href="${item.url}" class="text-decoration-none">
                                <div class="fw-bold">${item.title}</div>
                                <small class="text-muted">${item.description}</small>
                            </a>
                        </div>
                    `).join('');
                    this.searchResults.innerHTML = html;
                }
                this.showResults();
            }

            showResults() {
                this.searchResults.classList.remove('d-none');
            }

            hideResults() {
                this.searchResults.classList.add('d-none');
            }

            handleClickOutside(e) {
                if (!this.searchInput.contains(e.target) && !this.searchResults.contains(e.target)) {
                    this.hideResults();
                }
            }
        }

        // تهيئة المدراء عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            new ThemeManager();
            new SearchManager();
        });

        // دوال مساعدة للجداول التفاعلية
        function sortTable(table, column, direction = 'asc') {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            rows.sort((a, b) => {
                const aVal = a.cells[column].textContent.trim();
                const bVal = b.cells[column].textContent.trim();
                
                if (direction === 'asc') {
                    return aVal.localeCompare(bVal, 'ar', { numeric: true });
                } else {
                    return bVal.localeCompare(aVal, 'ar', { numeric: true });
                }
            });
            
            rows.forEach(row => tbody.appendChild(row));
        }

        // دالة تأكيد الحذف
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
            return confirm(message);
        }

        // دالة عرض الرسائل
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container-fluid');
            container.insertBefore(alertDiv, container.firstChild);
            
            // إزالة تلقائية بعد 5 ثوان
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>