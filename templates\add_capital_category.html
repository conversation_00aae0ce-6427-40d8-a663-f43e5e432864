{% extends "base.html" %}

{% block title %}إضافة فئة رأس مال - نظام إدارة الشركات{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card fade-in-up">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="bi bi-plus-circle text-success me-2"></i>
                        إضافة فئة رأس مال جديدة
                    </h4>
                    <a href="{{ url_for('capital_categories') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-arrow-right me-1"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label fw-bold">
                                <i class="bi bi-tag text-primary me-1"></i>
                                اسم الفئة *
                            </label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   placeholder="مثال: فئة 10,000 دينار" required>
                            <div class="form-text text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                اسم وصفي للفئة
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="amount" class="form-label fw-bold">
                                <i class="bi bi-cash text-success me-1"></i>
                                المبلغ (دينار) *
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="amount" name="amount" 
                                       placeholder="10000" min="1" required>
                                <span class="input-group-text">دينار</span>
                            </div>
                            <div class="form-text text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                مبلغ رأس المال بالدينار
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="max_companies" class="form-label fw-bold">
                                <i class="bi bi-building text-info me-1"></i>
                                الحد الأقصى للشركات *
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="max_companies" name="max_companies" 
                                       placeholder="10" min="1" value="10" required>
                                <span class="input-group-text">شركة</span>
                            </div>
                            <div class="form-text text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                عدد الشركات المسموح بها في هذه الفئة
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold text-muted">معاينة سريعة</label>
                            <div class="border rounded p-3 bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">إجمالي رأس المال المتوقع:</span>
                                    <strong class="text-success" id="totalCapitalPreview">0 دينار</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="description" class="form-label fw-bold">
                            <i class="bi bi-card-text text-warning me-1"></i>
                            الوصف
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف اختياري للفئة (مثل: فئة مخصصة للمشاريع الصغيرة والمتوسطة)"></textarea>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('capital_categories') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i>
                            إلغاء والعودة
                        </a>
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="bi bi-check-circle me-2"></i>
                            إضافة الفئة
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Examples Card -->
        <div class="card mt-4 slide-in-right" style="animation-delay: 0.3s;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightbulb text-warning me-2"></i>
                    أمثلة على فئات رأس المال
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>فئات صغيرة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-circle-fill text-success"></i> فئة 10,000 دينار</li>
                            <li><i class="bi bi-circle-fill text-success"></i> فئة 25,000 دينار</li>
                            <li><i class="bi bi-circle-fill text-success"></i> فئة 50,000 دينار</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>فئات كبيرة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-circle-fill text-primary"></i> فئة 100,000 دينار</li>
                            <li><i class="bi bi-circle-fill text-primary"></i> فئة 250,000 دينار</li>
                            <li><i class="bi bi-circle-fill text-primary"></i> فئة 500,000 دينار</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تحديث اسم الفئة تلقائياً عند إدخال المبلغ
document.getElementById('amount').addEventListener('input', function() {
    const amount = this.value;
    const nameField = document.getElementById('name');
    if (amount && !nameField.value) {
        nameField.value = `فئة ${parseInt(amount).toLocaleString()} دينار`;
    }
    updatePreview();
});

// تحديث المعاينة السريعة
document.getElementById('max_companies').addEventListener('input', updatePreview);

function updatePreview() {
    const amount = document.getElementById('amount').value;
    const maxCompanies = document.getElementById('max_companies').value;
    const previewElement = document.getElementById('totalCapitalPreview');
    
    if (amount && maxCompanies) {
        const total = parseInt(amount) * parseInt(maxCompanies);
        previewElement.textContent = `${total.toLocaleString()} دينار`;
        previewElement.className = 'text-success fw-bold';
    } else {
        previewElement.textContent = '0 دينار';
        previewElement.className = 'text-muted';
    }
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات التفاعل للحقول
    const inputs = document.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('shadow-sm');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('shadow-sm');
        });
    });
    
    // تحديث المعاينة الأولية
    updatePreview();
});

// التحقق من صحة النموذج
document.querySelector('form').addEventListener('submit', function(e) {
    const amount = document.getElementById('amount').value;
    const maxCompanies = document.getElementById('max_companies').value;
    
    if (parseInt(amount) < 1000) {
        e.preventDefault();
        alert('يجب أن يكون مبلغ رأس المال 1000 دينار على الأقل');
        return false;
    }
    
    if (parseInt(maxCompanies) < 1) {
        e.preventDefault();
        alert('يجب أن يكون الحد الأقصى للشركات شركة واحدة على الأقل');
        return false;
    }
    
    // عرض رسالة تحميل
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الإضافة...';
    submitBtn.disabled = true;
    
    // إعادة تفعيل الزر في حالة فشل الإرسال
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 5000);
});
</script>
{% endblock %}