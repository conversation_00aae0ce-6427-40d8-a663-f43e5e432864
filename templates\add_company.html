{% extends "base.html" %}

{% block title %}إضافة شركة جديدة - {{ category.name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-building-add"></i>
                    إضافة شركة جديدة - {{ category.name }}
                </h4>
                <small class="text-muted">
                    الشركات الحالية: {{ category.current_companies_count }} من {{ category.max_companies }}
                </small>
            </div>
            <div class="card-body">
                <form method="POST">
                    <!-- Company Information -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="bi bi-building"></i>
                                معلومات الشركة
                            </h5>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_name" class="form-label">اسم الشركة *</label>
                            <input type="text" class="form-control" id="company_name" name="company_name" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="project_plan" class="form-label">خطة المشروع *</label>
                        <textarea class="form-control" id="project_plan" name="project_plan" rows="4" 
                                  placeholder="اكتب خطة المشروع التفصيلية..." required></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="resources" class="form-label">الموارد</label>
                            <textarea class="form-control" id="resources" name="resources" rows="3"
                                      placeholder="الموارد المطلوبة للمشروع..."></textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="work_method" class="form-label">طريقة العمل</label>
                            <textarea class="form-control" id="work_method" name="work_method" rows="3"
                                      placeholder="طريقة تنفيذ المشروع..."></textarea>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <!-- Owner Information -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="text-success mb-3">
                                <i class="bi bi-person-circle"></i>
                                معلومات المالك الأساسي
                            </h5>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="owner_name" class="form-label">اسم المالك *</label>
                            <input type="text" class="form-control" id="owner_name" name="owner_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="owner_national_id" class="form-label">رقم الهوية</label>
                            <input type="text" class="form-control" id="owner_national_id" name="owner_national_id"
                                   placeholder="1234567890">
                            <div class="form-text">سيتم البحث عن المالك إذا كان مسجلاً مسبقاً</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="owner_phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="owner_phone" name="owner_phone"
                                   placeholder="05xxxxxxxx">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="owner_email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="owner_email" name="owner_email"
                                   placeholder="<EMAIL>">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="owner_address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="owner_address" name="owner_address" rows="2"
                                  placeholder="عنوان المالك..."></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('category_companies', category_id=category.id) }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-right"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle"></i>
                            إضافة الشركة
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Info Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li><i class="bi bi-check-circle text-success"></i> سيتم ربط الشركة بفئة رأس المال: <strong>{{ category.name }}</strong></li>
                    <li><i class="bi bi-check-circle text-success"></i> إذا كان المالك مسجلاً مسبقاً (برقم الهوية)، سيتم ربطه تلقائياً</li>
                    <li><i class="bi bi-check-circle text-success"></i> يمكن إضافة ملاك إضافيين لاحقاً من صفحة تفاصيل الشركة</li>
                    <li><i class="bi bi-check-circle text-success"></i> يمكن رفع الملفات والمستندات بعد إنشاء الشركة</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-format phone number
document.getElementById('owner_phone').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, '');
    if (value.length > 0 && !value.startsWith('05')) {
        if (value.startsWith('5')) {
            value = '0' + value;
        }
    }
    this.value = value;
});

// Check for existing owner when national ID is entered
document.getElementById('owner_national_id').addEventListener('blur', function() {
    const nationalId = this.value;
    if (nationalId.length >= 10) {
        // Here you could add AJAX call to check if owner exists
        // For now, just show a message
        console.log('Checking for existing owner with ID:', nationalId);
    }
});
</script>
{% endblock %}