#!/usr/bin/env python3
"""
تطبيق إدارة الشركات
Company Management System

لتشغيل التطبيق:
python run.py
"""

import os
from app import create_app, db
from models import User, CapitalCategory, Person, Company

# إنشاء التطبيق
app = create_app()

def init_database():
    """إعداد قاعدة البيانات الأولي"""
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        
        # التحقق من وجود مستخدم افتراضي
        if not User.query.first():
            print("إنشاء المستخدم الافتراضي...")
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            
            # إضافة فئات رأس مال افتراضية
            print("إضافة فئات رأس المال الافتراضية...")
            default_categories = [
                {'name': 'فئة 10,000 دينار', 'amount': 10000, 'max_companies': 10, 'description': 'فئة رأس المال الصغير للمشاريع الناشئة'},
                {'name': 'فئة 25,000 دينار', 'amount': 25000, 'max_companies': 8, 'description': 'فئة رأس المال المتوسط للمشاريع الصغيرة'},
                {'name': 'فئة 50,000 دينار', 'amount': 50000, 'max_companies': 6, 'description': 'فئة رأس المال المتوسط للمشاريع المتنامية'},
                {'name': 'فئة 100,000 دينار', 'amount': 100000, 'max_companies': 5, 'description': 'فئة رأس المال الكبير للمشاريع المتقدمة'},
                {'name': 'فئة 250,000 دينار', 'amount': 250000, 'max_companies': 3, 'description': 'فئة رأس المال الكبير للمشاريع الكبرى'},
            ]
            
            for cat_data in default_categories:
                category = CapitalCategory(**cat_data)
                db.session.add(category)
            
            # إضافة بعض الأشخاص التجريبيين
            print("إضافة أشخاص تجريبيين...")
            sample_people = [
                {
                    'name': 'أحمد محمد العلي',
                    'national_id': '1234567890',
                    'phone': '0501234567',
                    'email': '<EMAIL>',
                    'address': 'الرياض، المملكة العربية السعودية'
                },
                {
                    'name': 'فاطمة سعد الأحمد',
                    'national_id': '2345678901',
                    'phone': '0509876543',
                    'email': '<EMAIL>',
                    'address': 'جدة، المملكة العربية السعودية'
                },
                {
                    'name': 'محمد عبدالله الخالد',
                    'phone': '0555555555',
                    'email': '<EMAIL>',
                    'address': 'الدمام، المملكة العربية السعودية'
                }
            ]
            
            for person_data in sample_people:
                person = Person(**person_data)
                db.session.add(person)
            
            db.session.commit()
            print("تم إعداد قاعدة البيانات بنجاح!")
            print("\nمعلومات تسجيل الدخول:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: admin123")
        else:
            print("قاعدة البيانات موجودة مسبقاً")

def show_info():
    """عرض معلومات التطبيق"""
    print("=" * 50)
    print("🏢 نظام إدارة الشركات")
    print("Company Management System")
    print("=" * 50)
    print(f"🌐 رابط التطبيق: http://localhost:5000")
    print(f"👤 المستخدم الافتراضي: admin")
    print(f"🔑 كلمة المرور: admin123")
    print("=" * 50)
    print("الميزات المتاحة:")
    print("✅ إدارة فئات رأس المال")
    print("✅ إدارة الشركات والمشاريع")
    print("✅ إدارة الأشخاص والملاك")
    print("✅ رفع الملفات والمستندات")
    print("✅ واجهة عربية متجاوبة")
    print("=" * 50)

if __name__ == '__main__':
    # إعداد قاعدة البيانات
    init_database()
    
    # عرض معلومات التطبيق
    show_info()
    
    # تشغيل التطبيق
    print("🚀 بدء تشغيل التطبيق...")
    print("للإيقاف: اضغط Ctrl+C")
    print("-" * 50)
    
    app.run(
        debug=True,
        host='0.0.0.0',
        port=5000,
        use_reloader=True
    )