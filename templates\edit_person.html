{% extends "base.html" %}

{% block title %}تعديل بيانات الشخص - نظام إدارة الشركات{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card fade-in-up">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="bi bi-person-gear text-warning me-2"></i>
                        تعديل بيانات الشخص
                    </h4>
                    <a href="{{ url_for('people') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-arrow-right me-1"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label fw-bold">
                                <i class="bi bi-person text-primary me-1"></i>
                                الاسم الكامل *
                            </label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ person.name }}" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="national_id" class="form-label fw-bold">
                                <i class="bi bi-card-text text-info me-1"></i>
                                رقم الهوية
                            </label>
                            <input type="text" class="form-control" id="national_id" name="national_id" 
                                   value="{{ person.national_id or '' }}" placeholder="رقم الهوية الوطنية">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label fw-bold">
                                <i class="bi bi-telephone text-success me-1"></i>
                                رقم الهاتف
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ person.phone or '' }}" placeholder="رقم الهاتف">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label fw-bold">
                                <i class="bi bi-envelope text-info me-1"></i>
                                البريد الإلكتروني
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ person.email or '' }}" placeholder="البريد الإلكتروني">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label fw-bold">
                            <i class="bi bi-geo-alt text-warning me-1"></i>
                            العنوان
                        </label>
                        <textarea class="form-control" id="address" name="address" rows="2" 
                                  placeholder="العنوان الكامل">{{ person.address or '' }}</textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label for="notes" class="form-label fw-bold">
                            <i class="bi bi-card-text text-secondary me-1"></i>
                            ملاحظات
                        </label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="ملاحظات إضافية">{{ person.notes or '' }}</textarea>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('people') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i>
                            إلغاء والعودة
                        </a>
                        <button type="submit" class="btn btn-warning btn-lg">
                            <i class="bi bi-check-circle me-2"></i>
                            حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="card mt-4 slide-in-right" style="animation-delay: 0.3s;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle text-info me-2"></i>
                    معلومات إضافية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>الشركات المملوكة:</h6>
                        {% if person.owned_companies %}
                        <ul class="list-unstyled">
                            {% for company in person.owned_companies %}
                            {% if company.is_active %}
                            <li class="mb-2">
                                <span class="badge bg-primary me-2">{{ company.name }}</span>
                                <small class="text-muted">{{ company.capital_category.name }}</small>
                            </li>
                            {% endif %}
                            {% endfor %}
                        </ul>
                        {% else %}
                        <p class="text-muted">لا يملك أي شركات</p>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6">
                        <h6>تاريخ التسجيل:</h6>
                        <p class="text-muted">{{ person.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        
                        {% if person.updated_at and person.updated_at != person.created_at %}
                        <h6>آخر تحديث:</h6>
                        <p class="text-muted">{{ person.updated_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات التفاعل للحقول
    const inputs = document.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('shadow-sm');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('shadow-sm');
        });
    });
});

// التحقق من صحة النموذج
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    
    if (name.length < 2) {
        e.preventDefault();
        alert('يجب أن يكون الاسم مكوناً من حرفين على الأقل');
        return false;
    }
    
    // عرض رسالة تحميل
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
    submitBtn.disabled = true;
    
    // إعادة تفعيل الزر في حالة فشل الإرسال
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 5000);
});
</script>
{% endblock %}