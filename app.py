import os
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_from_directory
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash
from datetime import datetime
import uuid
from PIL import Image

from config import config
from models import db, User, CapitalCategory, Person, Company, CompanyOwnership, CompanyFile, SystemBackup

def create_app(config_name=None):
    app = Flask(__name__)
    
    # Load configuration
    config_name = config_name or os.environ.get('FLASK_CONFIG', 'default')
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    db.init_app(app)
    
    # Setup Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Create upload directory
    upload_dir = os.path.join(app.instance_path, app.config['UPLOAD_FOLDER'])
    os.makedirs(upload_dir, exist_ok=True)
    
    # Helper functions
    def allowed_file(filename):
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']
    
    def save_uploaded_file(file, company_id):
        """حفظ الملف المرفوع وإرجاع معلوماته"""
        if file and allowed_file(file.filename):
            # إنشاء اسم ملف فريد
            file_extension = file.filename.rsplit('.', 1)[1].lower()
            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
            
            # إنشاء مجلد للشركة إذا لم يكن موجوداً
            company_folder = os.path.join(upload_dir, f"company_{company_id}")
            os.makedirs(company_folder, exist_ok=True)
            
            # حفظ الملف
            file_path = os.path.join(company_folder, unique_filename)
            file.save(file_path)
            
            # تحديد نوع الملف
            file_type = 'image' if file_extension in ['jpg', 'jpeg', 'png', 'gif'] else 'document'
            
            return {
                'filename': unique_filename,
                'original_filename': file.filename,
                'file_path': file_path,
                'file_type': file_type,
                'file_size': os.path.getsize(file_path),
                'mime_type': file.content_type
            }
        return None
    
    # Routes
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return render_template('index.html')
    
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            username = request.form['username']
            password = request.form['password']
            
            user = User.query.filter_by(username=username).first()
            
            if user and user.check_password(password):
                login_user(user, remember=True)
                next_page = request.args.get('next')
                return redirect(next_page) if next_page else redirect(url_for('dashboard'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
        
        return render_template('login.html')
    
    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('تم تسجيل الخروج بنجاح', 'success')
        return redirect(url_for('index'))
    
    @app.route('/dashboard')
    @login_required
    def dashboard():
        from datetime import datetime
        
        # إحصائيات سريعة
        total_categories = CapitalCategory.query.filter_by(is_active=True).count()
        total_companies = Company.query.filter_by(is_active=True).count()
        total_people = Person.query.count()
        
        # حساب الأماكن المتاحة
        available_slots = 0
        categories = CapitalCategory.query.filter_by(is_active=True).all()
        for category in categories:
            companies_count = Company.query.filter_by(
                capital_category_id=category.id,
                is_active=True
            ).count()
            available_slots += max(0, category.max_companies - companies_count)
        
        # أحدث الشركات
        recent_companies = Company.query.filter_by(is_active=True).order_by(Company.created_at.desc()).limit(5).all()
        
        # فئات رأس المال مع إحصائياتها
        categories_stats = []
        
        for category in categories:
            companies_count = Company.query.filter_by(
                capital_category_id=category.id,
                is_active=True
            ).count()
            
            categories_stats.append({
                'category': category,
                'companies_count': companies_count,
                'available_slots': category.max_companies - companies_count,
                'usage_percentage': (companies_count / category.max_companies * 100) if category.max_companies > 0 else 0
            })
        
        return render_template('dashboard.html', 
                             total_categories=total_categories,
                             total_companies=total_companies,
                             total_people=total_people,
                             available_slots=available_slots,
                             recent_companies=recent_companies,
                             categories_stats=categories_stats,
                             current_date=datetime.now())
    
    @app.route('/capital-categories')
    @login_required
    def capital_categories():
        categories = CapitalCategory.query.filter_by(is_active=True).order_by(CapitalCategory.amount).all()
        return render_template('capital_categories.html', categories=categories)
    
    @app.route('/capital-categories/add', methods=['GET', 'POST'])
    @login_required
    def add_capital_category():
        if request.method == 'POST':
            name = request.form['name']
            amount = int(request.form['amount'])
            max_companies = int(request.form['max_companies'])
            description = request.form.get('description', '')
            
            category = CapitalCategory(
                name=name,
                amount=amount,
                max_companies=max_companies,
                description=description
            )
            
            db.session.add(category)
            db.session.commit()
            
            flash(f'تم إضافة فئة رأس المال "{name}" بنجاح', 'success')
            return redirect(url_for('capital_categories'))
        
        return render_template('add_capital_category.html')
    
    @app.route('/capital-categories/<int:category_id>/companies')
    @login_required
    def category_companies(category_id):
        category = CapitalCategory.query.get_or_404(category_id)
        companies = Company.query.filter_by(capital_category_id=category_id, is_active=True).all()
        return render_template('category_companies.html', category=category, companies=companies)
    
    @app.route('/companies/add/<int:category_id>', methods=['GET', 'POST'])
    @login_required
    def add_company(category_id):
        category = CapitalCategory.query.get_or_404(category_id)
        
        if not category.can_add_company:
            flash(f'لا يمكن إضافة شركة جديدة. تم الوصول للحد الأقصى ({category.max_companies}) في هذه الفئة', 'error')
            return redirect(url_for('category_companies', category_id=category_id))
        
        if request.method == 'POST':
            # بيانات الشركة
            company_name = request.form['company_name']
            project_plan = request.form['project_plan']
            resources = request.form.get('resources', '')
            work_method = request.form.get('work_method', '')
            
            # بيانات المالك الأساسي
            owner_name = request.form['owner_name']
            owner_national_id = request.form.get('owner_national_id', '')
            owner_phone = request.form.get('owner_phone', '')
            owner_email = request.form.get('owner_email', '')
            owner_address = request.form.get('owner_address', '')
            
            # التحقق من وجود المالك مسبقاً
            owner = None
            if owner_national_id:
                owner = Person.query.filter_by(national_id=owner_national_id).first()
            
            if not owner:
                # إنشاء مالك جديد
                owner = Person(
                    name=owner_name,
                    national_id=owner_national_id if owner_national_id else None,
                    phone=owner_phone,
                    email=owner_email,
                    address=owner_address
                )
                db.session.add(owner)
                db.session.flush()  # للحصول على ID المالك
            
            # إنشاء الشركة
            company = Company(
                name=company_name,
                project_plan=project_plan,
                resources=resources,
                work_method=work_method,
                capital_category_id=category_id,
                primary_owner_id=owner.id
            )
            
            db.session.add(company)
            db.session.commit()
            
            flash(f'تم إضافة الشركة "{company_name}" بنجاح', 'success')
            return redirect(url_for('category_companies', category_id=category_id))
        
        return render_template('add_company.html', category=category)
    
    @app.route('/companies/<int:company_id>')
    @login_required
    def company_details(company_id):
        company = Company.query.get_or_404(company_id)
        return render_template('company_details.html', company=company)
    

    @app.route('/companies/<int:company_id>/upload', methods=['POST'])
    @login_required
    def upload_company_file(company_id):
        company = Company.query.get_or_404(company_id)
        
        if 'file' not in request.files:
            flash('لم يتم اختيار ملف', 'error')
            return redirect(url_for('company_details', company_id=company_id))
        
        file = request.files['file']
        description = request.form.get('description', '')
        
        if file.filename == '':
            flash('لم يتم اختيار ملف', 'error')
            return redirect(url_for('company_details', company_id=company_id))
        
        file_info = save_uploaded_file(file, company_id)
        if file_info:
            company_file = CompanyFile(
                company_id=company_id,
                filename=file_info['filename'],
                original_filename=file_info['original_filename'],
                file_path=file_info['file_path'],
                file_type=file_info['file_type'],
                file_size=file_info['file_size'],
                mime_type=file_info['mime_type'],
                description=description,
                uploaded_by=current_user.id
            )
            
            db.session.add(company_file)
            db.session.commit()
            
            flash('تم رفع الملف بنجاح', 'success')
        else:
            flash('نوع الملف غير مدعوم', 'error')
        
        return redirect(url_for('company_details', company_id=company_id))
    
    @app.route('/people')
    @login_required
    def people():
        people = Person.query.order_by(Person.name).all()
        return render_template('people.html', people=people)
    
    @app.route('/people/add', methods=['GET', 'POST'])
    @login_required
    def add_person():
        if request.method == 'POST':
            name = request.form['name']
            national_id = request.form.get('national_id', '')
            phone = request.form.get('phone', '')
            email = request.form.get('email', '')
            address = request.form.get('address', '')
            notes = request.form.get('notes', '')
            
            person = Person(
                name=name,
                national_id=national_id if national_id else None,
                phone=phone,
                email=email,
                address=address,
                notes=notes
            )
            
            db.session.add(person)
            db.session.commit()
            
            flash(f'تم إضافة الشخص "{name}" بنجاح', 'success')
            return redirect(url_for('people'))
        
        return render_template('add_person.html')
    
    # API للبحث السريع
    @app.route('/api/search')
    @login_required
    def api_search():
        query = request.args.get('q', '').strip()
        if len(query) < 2:
            return jsonify([])
        
        results = []
        
        # البحث في فئات رأس المال
        categories = CapitalCategory.query.filter(
            CapitalCategory.name.contains(query),
            CapitalCategory.is_active == True
        ).limit(5).all()
        
        for category in categories:
            results.append({
                'title': category.name,
                'description': f'فئة رأس مال - {category.description or ""}',
                'url': url_for('category_companies', category_id=category.id)
            })
        
        # البحث في الشركات
        companies = Company.query.filter(
            Company.name.contains(query),
            Company.is_active == True
        ).limit(5).all()
        
        for company in companies:
            results.append({
                'title': company.name,
                'description': f'شركة - {company.capital_category.name}',
                'url': url_for('company_details', company_id=company.id)
            })
        
        # البحث في الأشخاص
        people = Person.query.filter(
            Person.name.contains(query)
        ).limit(5).all()
        
        for person in people:
            results.append({
                'title': person.name,
                'description': f'شخص - {person.phone or "لا يوجد هاتف"}',
                'url': f'/people/{person.id}'  # سنضيف هذا المسار لاحقاً
            })
        
        return jsonify(results[:10])  # أقصى 10 نتائج
    
    # API للإحصائيات
    @app.route('/api/stats')
    @login_required
    def api_stats():
        stats = {
            'total_categories': CapitalCategory.query.filter_by(is_active=True).count(),
            'total_companies': Company.query.filter_by(is_active=True).count(),
            'total_people': Person.query.count(),
            'categories_usage': []
        }
        
        categories = CapitalCategory.query.filter_by(is_active=True).all()
        for category in categories:
            companies_count = Company.query.filter_by(
                capital_category_id=category.id,
                is_active=True
            ).count()
            
            stats['categories_usage'].append({
                'name': category.name,
                'current': companies_count,
                'max': category.max_companies,
                'percentage': (companies_count / category.max_companies * 100) if category.max_companies > 0 else 0
            })
        
        return jsonify(stats)
    
    # مسارات التعديل والحذف
    @app.route('/capital-categories/<int:category_id>/edit', methods=['GET', 'POST'])
    @login_required
    def edit_capital_category(category_id):
        category = CapitalCategory.query.get_or_404(category_id)
        
        if request.method == 'POST':
            category.name = request.form['name']
            category.amount = int(request.form['amount'])
            category.max_companies = int(request.form['max_companies'])
            category.description = request.form.get('description', '')
            
            db.session.commit()
            flash(f'تم تحديث فئة "{category.name}" بنجاح', 'success')
            return redirect(url_for('capital_categories'))
        
        return render_template('edit_capital_category.html', category=category)
    
    @app.route('/capital-categories/<int:category_id>/delete', methods=['POST'])
    @login_required
    def delete_capital_category(category_id):
        category = CapitalCategory.query.get_or_404(category_id)
        
        # التحقق من وجود شركات مرتبطة
        companies_count = Company.query.filter_by(capital_category_id=category_id, is_active=True).count()
        if companies_count > 0:
            flash(f'لا يمكن حذف الفئة "{category.name}" لأنها تحتوي على {companies_count} شركة', 'error')
            return redirect(url_for('capital_categories'))
        
        category.is_active = False
        db.session.commit()
        flash(f'تم حذف فئة "{category.name}" بنجاح', 'success')
        return redirect(url_for('capital_categories'))
    
    @app.route('/companies/<int:company_id>/edit', methods=['GET', 'POST'])
    @login_required
    def edit_company(company_id):
        company = Company.query.get_or_404(company_id)
        
        if request.method == 'POST':
            company.name = request.form['name']
            company.project_plan = request.form['project_plan']
            company.resources = request.form.get('resources', '')
            company.work_method = request.form.get('work_method', '')
            
            # تحديث الشركاء
            company.partners.clear()
            partner_ids = request.form.getlist('partners')
            for partner_id in partner_ids:
                if partner_id:
                    partner = Person.query.get(int(partner_id))
                    if partner:
                        company.partners.append(partner)
            
            db.session.commit()
            flash(f'تم تحديث الشركة "{company.name}" بنجاح', 'success')
            return redirect(url_for('company_details', company_id=company.id))
        
        people = Person.query.order_by(Person.name).all()
        return render_template('edit_company.html', company=company, people=people)
    
    @app.route('/companies/<int:company_id>/delete', methods=['POST'])
    @login_required
    def delete_company(company_id):
        company = Company.query.get_or_404(company_id)
        company.is_active = False
        db.session.commit()
        flash(f'تم حذف الشركة "{company.name}" بنجاح', 'success')
        return redirect(url_for('category_companies', category_id=company.capital_category_id))
    
    @app.route('/people/<int:person_id>/edit', methods=['GET', 'POST'])
    @login_required
    def edit_person(person_id):
        person = Person.query.get_or_404(person_id)
        
        if request.method == 'POST':
            person.name = request.form['name']
            person.national_id = request.form.get('national_id', '')
            person.phone = request.form.get('phone', '')
            person.email = request.form.get('email', '')
            person.address = request.form.get('address', '')
            person.notes = request.form.get('notes', '')
            
            db.session.commit()
            flash(f'تم تحديث بيانات "{person.name}" بنجاح', 'success')
            return redirect(url_for('people'))
        
        return render_template('edit_person.html', person=person)
    
    @app.route('/people/<int:person_id>/delete', methods=['POST'])
    @login_required
    def delete_person(person_id):
        person = Person.query.get_or_404(person_id)
        
        # التحقق من وجود شركات مرتبطة
        owned_companies = Company.query.filter_by(primary_owner_id=person_id, is_active=True).count()
        if owned_companies > 0:
            flash(f'لا يمكن حذف "{person.name}" لأنه يملك {owned_companies} شركة نشطة', 'error')
            return redirect(url_for('people'))
        
        # حذف الشخص
        db.session.delete(person)
        db.session.commit()
        flash(f'تم حذف "{person.name}" بنجاح', 'success')
        return redirect(url_for('people'))
    
    # Initialize database
    with app.app_context():
        db.create_all()
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)