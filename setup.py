#!/usr/bin/env python3
"""
إعداد وتهيئة نظام إدارة الشركات
Setup and initialization for Company Management System
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المكتبات المطلوبة...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ تم تثبيت جميع المكتبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المكتبات: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        'uploads',
        'backups',
        'logs',
        'instance'
    ]
    
    print("📁 إنشاء المجلدات المطلوبة...")
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"   ✅ تم إنشاء مجلد: {directory}")
        else:
            print(f"   ℹ️  مجلد موجود: {directory}")

def create_env_file():
    """إنشاء ملف .env من المثال"""
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if not env_file.exists() and env_example.exists():
        print("⚙️ إنشاء ملف الإعدادات...")
        
        # نسخ ملف المثال
        with open(env_example, 'r', encoding='utf-8') as src:
            content = src.read()
        
        # إنشاء مفتاح أمان عشوائي
        import secrets
        secret_key = secrets.token_urlsafe(32)
        content = content.replace('your-very-secret-key-change-this-in-production', secret_key)
        
        with open(env_file, 'w', encoding='utf-8') as dst:
            dst.write(content)
        
        print("   ✅ تم إنشاء ملف .env")
        print("   ⚠️  يرجى مراجعة الإعدادات في ملف .env")
    else:
        print("   ℹ️  ملف .env موجود مسبقاً")

def initialize_database():
    """تهيئة قاعدة البيانات"""
    print("🗄️ تهيئة قاعدة البيانات...")
    
    try:
        from app import create_app, db
        from models import User, CapitalCategory, Person
        
        app = create_app()
        with app.app_context():
            # إنشاء الجداول
            db.create_all()
            print("   ✅ تم إنشاء جداول قاعدة البيانات")
            
            # التحقق من وجود مستخدم افتراضي
            if not User.query.first():
                print("   👤 إنشاء المستخدم الافتراضي...")
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    is_admin=True
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                
                # إضافة فئات رأس مال افتراضية
                print("   💰 إضافة فئات رأس المال الافتراضية...")
                default_categories = [
                    {
                        'name': 'فئة 10,000 دينار',
                        'amount': 10000,
                        'max_companies': 10,
                        'description': 'فئة رأس المال الصغير للمشاريع الناشئة'
                    },
                    {
                        'name': 'فئة 25,000 دينار',
                        'amount': 25000,
                        'max_companies': 8,
                        'description': 'فئة رأس المال المتوسط للمشاريع الصغيرة'
                    },
                    {
                        'name': 'فئة 50,000 دينار',
                        'amount': 50000,
                        'max_companies': 6,
                        'description': 'فئة رأس المال المتوسط للمشاريع المتنامية'
                    },
                    {
                        'name': 'فئة 100,000 دينار',
                        'amount': 100000,
                        'max_companies': 5,
                        'description': 'فئة رأس المال الكبير للمشاريع المتقدمة'
                    }
                ]
                
                for cat_data in default_categories:
                    category = CapitalCategory(**cat_data)
                    db.session.add(category)
                
                db.session.commit()
                print("   ✅ تم إنشاء البيانات الافتراضية")
            else:
                print("   ℹ️  قاعدة البيانات مهيأة مسبقاً")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في تهيئة قاعدة البيانات: {e}")
        return False

def run_tests():
    """تشغيل اختبارات بسيطة"""
    print("🧪 تشغيل الاختبارات...")
    
    try:
        # اختبار استيراد المكتبات الأساسية
        import flask
        import flask_sqlalchemy
        import flask_login
        print("   ✅ جميع المكتبات متاحة")
        
        # اختبار إنشاء التطبيق
        from app import create_app
        app = create_app()
        print("   ✅ تم إنشاء التطبيق بنجاح")
        
        # اختبار الاتصال بقاعدة البيانات
        with app.app_context():
            from models import db, User
            user_count = User.query.count()
            print(f"   ✅ الاتصال بقاعدة البيانات ({user_count} مستخدم)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ فشل في الاختبارات: {e}")
        return False

def print_success_message():
    """طباعة رسالة النجاح"""
    print("\n" + "=" * 60)
    print("🎉 تم إعداد نظام إدارة الشركات بنجاح!")
    print("=" * 60)
    print("🚀 لتشغيل التطبيق:")
    print("   python run.py")
    print("   أو")
    print("   start_app.bat")
    print()
    print("🌐 رابط التطبيق: http://localhost:5000")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print()
    print("📚 للمساعدة:")
    print("   - اقرأ ملف README.md")
    print("   - راجع ملف .env للإعدادات")
    print("   - استخدم backup_manager.py للنسخ الاحتياطية")
    print("   - استخدم system_info.py لمراقبة النظام")
    print("=" * 60)

def main():
    """الدالة الرئيسية للإعداد"""
    print("🏢 إعداد نظام إدارة الشركات")
    print("Company Management System Setup")
    print("=" * 50)
    
    # التحقق من إصدار Python
    if not check_python_version():
        return False
    
    # تثبيت المتطلبات
    if not install_requirements():
        return False
    
    # إنشاء المجلدات
    create_directories()
    
    # إنشاء ملف الإعدادات
    create_env_file()
    
    # تهيئة قاعدة البيانات
    if not initialize_database():
        return False
    
    # تشغيل الاختبارات
    if not run_tests():
        print("⚠️  بعض الاختبارات فشلت، لكن يمكن المتابعة")
    
    # رسالة النجاح
    print_success_message()
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            print("\n❌ فشل في الإعداد")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إلغاء الإعداد")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)