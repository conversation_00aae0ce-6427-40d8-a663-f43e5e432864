#!/usr/bin/env python3
"""
تشغيل سريع لنظام إدارة الشركات
Quick start for Company Management System
"""

import os
import sys
import webbrowser
import time
from pathlib import Path

def check_setup():
    """التحقق من إعداد النظام"""
    current_dir = Path(__file__).parent
    
    # التحقق من الملفات الأساسية
    required_files = [
        'app.py',
        'models.py',
        'config.py',
        'requirements.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if not (current_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    # التحقق من المجلدات
    required_dirs = ['templates', 'static', 'uploads']
    missing_dirs = []
    for directory in required_dirs:
        if not (current_dir / directory).exists():
            missing_dirs.append(directory)
    
    if missing_dirs:
        print("❌ مجلدات مفقودة:")
        for directory in missing_dirs:
            print(f"   - {directory}")
        return False
    
    return True

def install_dependencies():
    """تثبيت المكتبات المطلوبة"""
    print("📦 التحقق من المكتبات المطلوبة...")
    
    try:
        import flask
        print("   ✅ Flask متوفر")
    except ImportError:
        print("   📥 تثبيت Flask...")
        os.system(f"{sys.executable} -m pip install flask")
    
    try:
        import flask_sqlalchemy
        print("   ✅ Flask-SQLAlchemy متوفر")
    except ImportError:
        print("   📥 تثبيت Flask-SQLAlchemy...")
        os.system(f"{sys.executable} -m pip install flask-sqlalchemy")
    
    try:
        import flask_login
        print("   ✅ Flask-Login متوفر")
    except ImportError:
        print("   📥 تثبيت Flask-Login...")
        os.system(f"{sys.executable} -m pip install flask-login")

def start_application():
    """تشغيل التطبيق"""
    print("🚀 بدء تشغيل التطبيق...")
    
    try:
        # استيراد التطبيق
        from run import create_app, init_database
        
        # تهيئة قاعدة البيانات
        print("🗄️ تهيئة قاعدة البيانات...")
        init_database()
        
        # إنشاء التطبيق
        app = create_app()
        
        print("\n" + "=" * 50)
        print("🏢 نظام إدارة الشركات جاهز!")
        print("=" * 50)
        print("🌐 الرابط: http://localhost:5000")
        print("👤 اسم المستخدم: admin")
        print("🔑 كلمة المرور: admin123")
        print("=" * 50)
        print("للإيقاف: اضغط Ctrl+C")
        print("-" * 50)
        
        # فتح المتصفح تلقائياً
        try:
            time.sleep(2)
            webbrowser.open('http://localhost:5000')
        except:
            pass
        
        # تشغيل التطبيق
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=False  # تجنب إعادة التشغيل المزدوج
        )
        
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف التطبيق")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        print("\nجرب تشغيل setup.py أولاً:")
        print("python setup.py")

def show_menu():
    """عرض القائمة الرئيسية"""
    print("\n" + "=" * 50)
    print("🏢 نظام إدارة الشركات - التشغيل السريع")
    print("Company Management System - Quick Start")
    print("=" * 50)
    print("1. تشغيل التطبيق")
    print("2. إعداد النظام")
    print("3. النسخ الاحتياطي")
    print("4. معلومات النظام")
    print("5. الأدوات الإدارية")
    print("0. خروج")
    print("-" * 50)

def main():
    """الدالة الرئيسية"""
    while True:
        show_menu()
        choice = input("اختر العملية المطلوبة: ").strip()
        
        try:
            if choice == '1':
                # تشغيل التطبيق
                if not check_setup():
                    print("\n⚠️  يجب إعداد النظام أولاً")
                    continue
                
                install_dependencies()
                start_application()
                
            elif choice == '2':
                # إعداد النظام
                print("🔧 تشغيل إعداد النظام...")
                os.system(f"{sys.executable} setup.py")
                
            elif choice == '3':
                # النسخ الاحتياطي
                print("💾 تشغيل مدير النسخ الاحتياطية...")
                os.system(f"{sys.executable} backup_manager.py")
                
            elif choice == '4':
                # معلومات النظام
                print("📊 تشغيل معلومات النظام...")
                os.system(f"{sys.executable} system_info.py")
                
            elif choice == '5':
                # الأدوات الإدارية
                print("🔧 تشغيل الأدوات الإدارية...")
                os.system(f"{sys.executable} admin_tools.py")
                
            elif choice == '0':
                print("👋 وداعاً!")
                break
                
            else:
                print("❌ اختيار غير صحيح")
                
        except KeyboardInterrupt:
            print("\n\n⏹️  تم الإلغاء")
        except Exception as e:
            print(f"\n❌ حدث خطأ: {e}")
        
        if choice != '1':  # لا نطلب Enter بعد تشغيل التطبيق
            input("\nاضغط Enter للمتابعة...")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 وداعاً!")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")