@echo off
chcp 65001 >nul
title نظام إدارة الشركات - Company Management System

echo.
echo ================================================
echo 🏢 نظام إدارة الشركات
echo Company Management System
echo ================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود المكتبات المطلوبة
echo 🔍 التحقق من المكتبات المطلوبة...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت المكتبات المطلوبة...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
)

echo ✅ جميع المتطلبات متوفرة
echo.

REM تشغيل التطبيق
echo 🚀 بدء تشغيل التطبيق...
echo.
echo 🌐 سيتم فتح التطبيق على: http://localhost:5000
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo للإيقاف: اضغط Ctrl+C
echo ================================================
echo.

python run.py

echo.
echo 👋 تم إيقاف التطبيق
pause