{% extends "base.html" %}

{% block title %}شركات {{ category.name }} - نظام إدارة الشركات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2>
            <i class="bi bi-building"></i>
            شركات {{ category.name }}
        </h2>
        <p class="text-muted mb-0">
            {{ category.current_companies_count }} من {{ category.max_companies }} شركة
        </p>
    </div>
    <div>
        <a href="{{ url_for('capital_categories') }}" class="btn btn-outline-secondary me-2">
            <i class="bi bi-arrow-right"></i>
            العودة للفئات
        </a>
        {% if category.can_add_company %}
        <a href="{{ url_for('add_company', category_id=category.id) }}" class="btn btn-success">
            <i class="bi bi-plus-circle"></i>
            إضافة شركة جديدة
        </a>
        {% endif %}
    </div>
</div>

<!-- Category Info Card -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-1">{{ category.name }}</h5>
                <p class="text-muted mb-0">{{ category.description or 'لا يوجد وصف' }}</p>
            </div>
            <div class="col-md-4 text-end">
                <span class="badge bg-primary fs-6">{{ "{:,}".format(category.amount) }} دينار</span>
                <div class="mt-2">
                    {% set progress_percentage = (category.current_companies_count / category.max_companies * 100) if category.max_companies > 0 else 0 %}
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar {% if progress_percentage >= 100 %}bg-danger{% elif progress_percentage >= 80 %}bg-warning{% else %}bg-success{% endif %}" 
                             style="width: {{ progress_percentage }}%"></div>
                    </div>
                    <small class="text-muted">{{ "%.0f"|format(progress_percentage) }}% مكتمل</small>
                </div>
            </div>
        </div>
    </div>
</div>

{% if companies %}
<div class="row">
    {% for company in companies %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-start">
                <h5 class="mb-0">{{ company.name }}</h5>
                <span class="badge bg-success">نشطة</span>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>المالك الأساسي:</strong>
                    <p class="text-muted mb-1">{{ company.primary_owner.name }}</p>
                    {% if company.primary_owner.phone %}
                    <small class="text-muted">
                        <i class="bi bi-telephone"></i> {{ company.primary_owner.phone }}
                    </small>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <strong>خطة المشروع:</strong>
                    <p class="text-muted small">
                        {{ company.project_plan[:100] }}{% if company.project_plan|length > 100 %}...{% endif %}
                    </p>
                </div>
                
                {% if company.additional_owners %}
                <div class="mb-3">
                    <strong>ملاك إضافيون:</strong>
                    <div class="d-flex flex-wrap gap-1">
                        {% for ownership in company.additional_owners %}
                        <span class="badge bg-info">{{ ownership.person.name }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <div class="row text-center small">
                    <div class="col-6">
                        <div class="border-end">
                            <strong>{{ company.files|length }}</strong>
                            <div class="text-muted">ملف</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <strong>{{ company.created_at.strftime('%Y-%m-%d') }}</strong>
                        <div class="text-muted">تاريخ الإضافة</div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="action-buttons">
                        <a href="{{ url_for('company_details', company_id=company.id) }}" 
                           class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                            <i class="bi bi-eye"></i>
                            التفاصيل
                        </a>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="{{ url_for('edit_company', company_id=company.id) }}" 
                           class="btn btn-outline-warning btn-sm" title="تعديل الشركة">
                            <i class="bi bi-pencil"></i>
                            تعديل
                        </a>
                        
                        <form method="POST" action="{{ url_for('delete_company', company_id=company.id) }}" 
                              class="d-inline" onsubmit="return confirmDelete('هل أنت متأكد من حذف شركة &quot;{{ company.name }}&quot;؟')">
                            <button type="submit" class="btn btn-outline-danger btn-sm" title="حذف الشركة">
                                <i class="bi bi-trash"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="text-center py-5">
    <i class="bi bi-building display-1 text-muted"></i>
    <h3 class="mt-3 text-muted">لا توجد شركات في هذه الفئة</h3>
    <p class="text-muted">ابدأ بإضافة شركة جديدة لهذه الفئة</p>
    {% if category.can_add_company %}
    <a href="{{ url_for('add_company', category_id=category.id) }}" class="btn btn-success">
        <i class="bi bi-plus-circle"></i>
        إضافة شركة جديدة
    </a>
    {% else %}
    <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle"></i>
        تم الوصول للحد الأقصى للشركات في هذه الفئة ({{ category.max_companies }})
    </div>
    {% endif %}
</div>
{% endif %}
{% endblock %}