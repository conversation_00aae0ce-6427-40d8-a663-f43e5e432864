# 🏢 نظام إدارة الشركات - معلومات المشروع
## Company Management System - Project Information

---

## 📋 نظرة عامة

نظام شامل لإدارة الشركات وفئات رأس المال والملاك مع واجهة عربية متجاوبة. تم تطويره باستخدام Flask وقاعدة بيانات SQLite مع إمكانية التوسع لقواعد بيانات أخرى.

## 🎯 الهدف من النظام

- إدارة فئات رأس المال المختلفة مع تحديد حد أقصى للشركات في كل فئة
- تسجيل الشركات مع خطط المشاريع والموارد وطرق العمل
- إدارة الأشخاص والملاك (مالك أساسي + ملاك إضافيين)
- رفع وإدارة الملفات والمستندات لكل شركة
- نظام نسخ احتياطي شامل

## 🏗️ هيكل المشروع

```
rt/
├── 📄 الملفات الأساسية
│   ├── app.py                 # التطبيق الرئيسي
│   ├── models.py              # نماذج قاعدة البيانات
│   ├── config.py              # إعدادات التطبيق
│   ├── run.py                 # ملف التشغيل الرئيسي
│   └── utils.py               # أدوات مساعدة
│
├── 🔧 أدوات الإدارة
│   ├── setup.py               # إعداد وتهيئة النظام
│   ├── backup_manager.py      # إدارة النسخ الاحتياطية
│   ├── system_info.py         # معلومات وإحصائيات النظام
│   ├── admin_tools.py         # أدوات إدارية متقدمة
│   └── quick_start.py         # تشغيل سريع
│
├── 🎨 واجهة المستخدم
│   ├── templates/             # قوالب HTML
│   │   ├── base.html          # القالب الأساسي
│   │   ├── dashboard.html     # لوحة التحكم
│   │   ├── capital_categories.html
│   │   ├── company_details.html
│   │   └── ...
│   └── static/
│       └── css/
│           └── style.css      # التنسيقات المخصصة
│
├── 📁 مجلدات البيانات
│   ├── uploads/               # الملفات المرفوعة
│   ├── backups/               # النسخ الاحتياطية
│   └── instance/              # ملفات التطبيق
│
└── 📚 التوثيق
    ├── README.md              # دليل المستخدم
    ├── PROJECT_INFO.md        # معلومات المشروع
    ├── requirements.txt       # المكتبات المطلوبة
    └── .env.example           # مثال على ملف الإعدادات
```

## 🗄️ قاعدة البيانات

### الجداول الرئيسية:

1. **users** - المستخدمين
   - id, username, email, password_hash, is_admin, created_at

2. **capital_category** - فئات رأس المال
   - id, name, amount, max_companies, description, is_active, created_at

3. **person** - الأشخاص
   - id, name, national_id, phone, email, address, notes, created_at

4. **company** - الشركات
   - id, name, project_plan, resources, work_method, capital_category_id, primary_owner_id, status, created_at, updated_at

5. **company_ownership** - ملكية الشركات
   - id, company_id, person_id, ownership_percentage, role, start_date, is_active

6. **company_file** - ملفات الشركات
   - id, company_id, filename, original_filename, file_path, file_type, file_size, uploaded_at

7. **system_backup** - النسخ الاحتياطية
   - id, filename, file_path, backup_type, file_size, created_at

## 🚀 طرق التشغيل

### 1. التشغيل السريع
```bash
python quick_start.py
```

### 2. التشغيل العادي
```bash
python run.py
```

### 3. التشغيل على Windows
```bash
start_app.bat
```

### 4. الإعداد الأولي
```bash
python setup.py
```

## 🔧 الأدوات المساعدة

### إدارة النسخ الاحتياطية
```bash
python backup_manager.py
```
- إنشاء نسخ احتياطية
- استعادة النسخ
- إدارة النسخ القديمة

### معلومات النظام
```bash
python system_info.py
```
- إحصائيات شاملة
- تحليل السعة
- فحص صحة النظام

### الأدوات الإدارية
```bash
python admin_tools.py
```
- إدارة المستخدمين
- تصدير البيانات
- تنظيف قاعدة البيانات

## 🔐 الأمان

### المصادقة
- نظام تسجيل دخول آمن
- تشفير كلمات المرور
- جلسات محمية

### حماية الملفات
- التحقق من أنواع الملفات المسموحة
- تشفير أسماء الملفات
- حماية من رفع ملفات خطيرة

### قاعدة البيانات
- حماية من SQL Injection
- تشفير البيانات الحساسة
- نسخ احتياطية منتظمة

## 🌐 الواجهة

### التصميم
- واجهة عربية كاملة (RTL)
- تصميم متجاوب (Bootstrap 5)
- أيقونات واضحة ومفهومة

### سهولة الاستخدام
- تنقل بديهي
- رسائل واضحة
- تأكيدات للعمليات المهمة

## 📊 الإحصائيات والتقارير

### لوحة التحكم
- إحصائيات سريعة
- أحدث الشركات
- حالة النظام

### التقارير
- تقارير مفصلة
- تحليل السعة
- إحصائيات الاستخدام

## 🔄 النسخ الاحتياطي

### أنواع النسخ
- نسخ يدوية
- نسخ تلقائية (مستقبلية)
- نسخ شاملة أو جزئية

### محتوى النسخة
- قاعدة البيانات
- الملفات المرفوعة
- ملفات الإعدادات

## 🛠️ التطوير والصيانة

### إضافة ميزات جديدة
1. تحديث models.py للجداول الجديدة
2. إضافة routes في app.py
3. إنشاء قوالب HTML جديدة
4. تحديث التنسيقات في style.css

### الصيانة الدورية
- تنظيف قاعدة البيانات
- تحسين الأداء
- تحديث النسخ الاحتياطية
- مراجعة السجلات

## 📈 التوسع المستقبلي

### قاعدة البيانات
- دعم PostgreSQL
- دعم MySQL
- تحسين الاستعلامات

### الميزات
- API للتكامل
- تطبيق موبايل
- إشعارات البريد الإلكتروني
- تقارير متقدمة

### الأمان
- مصادقة ثنائية
- تسجيل العمليات
- مراقبة الأنشطة

## 🎓 التعلم والتطوير

### التقنيات المستخدمة
- **Python** - لغة البرمجة الأساسية
- **Flask** - إطار عمل الويب
- **SQLAlchemy** - ORM لقاعدة البيانات
- **Bootstrap** - إطار عمل CSS
- **JavaScript** - التفاعل في المتصفح

### المفاهيم المطبقة
- MVC Architecture
- Database Design
- User Authentication
- File Management
- Responsive Design

## 📞 الدعم والمساعدة

### الملفات المرجعية
- README.md - دليل المستخدم الشامل
- .env.example - مثال على الإعدادات
- requirements.txt - المكتبات المطلوبة

### الأدوات المساعدة
- quick_start.py - للبدء السريع
- setup.py - للإعداد الأولي
- system_info.py - لمراقبة النظام

---

## 📝 ملاحظات مهمة

1. **الأمان**: غير كلمة المرور الافتراضية قبل الاستخدام في الإنتاج
2. **النسخ الاحتياطية**: قم بإنشاء نسخ احتياطية منتظمة
3. **التحديثات**: راقب تحديثات المكتبات المستخدمة
4. **الأداء**: راقب أداء النظام وحجم قاعدة البيانات

---

**تم تطوير هذا النظام بعناية لتلبية احتياجات إدارة الشركات بطريقة فعالة وآمنة.**