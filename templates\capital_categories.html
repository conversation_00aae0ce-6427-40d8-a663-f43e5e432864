{% extends "base.html" %}

{% block title %}فئات رأس المال - نظام إدارة الشركات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 fade-in-up">
    <div>
        <h2>
            <i class="bi bi-cash-stack text-success"></i>
            فئات رأس المال
        </h2>
        <small class="text-muted">إدارة وتنظيم فئات رؤوس الأموال بالدينار الليبي</small>
    </div>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
            <li class="breadcrumb-item active">فئات رأس المال</li>
        </ol>
    </nav>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3 fade-in-up" style="animation-delay: 0.1s;">
        <div class="card text-center stats-card">
            <div class="card-body">
                <div class="mb-2">
                    <i class="bi bi-cash-stack display-6"></i>
                </div>
                <h3 class="mb-1">{{ categories|length }}</h3>
                <p class="text-white-50 mb-0">إجمالي الفئات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 fade-in-up" style="animation-delay: 0.2s;">
        <div class="card text-center stats-card stats-card-success">
            <div class="card-body">
                <div class="mb-2">
                    <i class="bi bi-building display-6"></i>
                </div>
                <h3 class="mb-1">{{ categories|sum(attribute='current_companies')|default(0) }}</h3>
                <p class="text-white-50 mb-0">الشركات النشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 fade-in-up" style="animation-delay: 0.3s;">
        <div class="card text-center stats-card stats-card-warning">
            <div class="card-body">
                <div class="mb-2">
                    <i class="bi bi-currency-exchange display-6"></i>
                </div>
                <h3 class="mb-1">{{ "{:,}".format(categories|sum(attribute='amount')|default(0)) }}</h3>
                <p class="text-white-50 mb-0">إجمالي رؤوس الأموال</p>
                <small class="text-white-50">دينار ليبي</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 fade-in-up" style="animation-delay: 0.4s;">
        <div class="card text-center stats-card stats-card-info">
            <div class="card-body">
                <div class="mb-2">
                    <i class="bi bi-percent display-6"></i>
                </div>
                <h3 class="mb-1">{{ "%.1f"|format((categories|sum(attribute='current_companies')|default(0) / categories|sum(attribute='max_companies')|default(1) * 100)) }}%</h3>
                <p class="text-white-50 mb-0">معدل الإشغال</p>
            </div>
        </div>
    </div>
</div>

<!-- أزرار الإجراءات -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div class="action-buttons">
        <button class="btn btn-outline-info" data-bs-toggle="collapse" data-bs-target="#searchFilters">
            <i class="bi bi-funnel me-2"></i>
            فلاتر البحث
        </button>
        <button class="btn btn-outline-success" onclick="exportCategoriesData()">
            <i class="bi bi-download me-2"></i>
            تصدير البيانات
        </button>
    </div>
    <a href="{{ url_for('add_capital_category') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle me-2"></i>
        إضافة فئة جديدة
    </a>
</div>

<!-- فلاتر البحث المتقدم -->
<div class="collapse mb-4" id="searchFilters">
    <div class="card slide-in-right">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="bi bi-search text-primary"></i>
                فلاتر البحث المتقدم
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="searchName" class="form-label">البحث بالاسم</label>
                    <input type="text" class="form-control" id="searchName" placeholder="اسم الفئة...">
                </div>
                
                <div class="col-md-3 mb-3">
                    <label for="filterAmount" class="form-label">نطاق المبلغ</label>
                    <select class="form-select" id="filterAmount">
                        <option value="">جميع المبالغ</option>
                        <option value="0-25000">أقل من 25,000 دينار</option>
                        <option value="25000-100000">25,000 - 100,000 دينار</option>
                        <option value="100000-500000">100,000 - 500,000 دينار</option>
                        <option value="500000+">أكثر من 500,000 دينار</option>
                    </select>
                </div>
                
                <div class="col-md-3 mb-3">
                    <label for="filterUsage" class="form-label">معدل الإشغال</label>
                    <select class="form-select" id="filterUsage">
                        <option value="">جميع المعدلات</option>
                        <option value="empty">فارغة (0%)</option>
                        <option value="low">منخفض (1-50%)</option>
                        <option value="medium">متوسط (51-80%)</option>
                        <option value="high">عالي (81-99%)</option>
                        <option value="full">مكتملة (100%)</option>
                    </select>
                </div>
                
                <div class="col-md-3 mb-3">
                    <label for="sortBy" class="form-label">ترتيب حسب</label>
                    <select class="form-select" id="sortBy">
                        <option value="name">الاسم</option>
                        <option value="amount">المبلغ</option>
                        <option value="usage">معدل الإشغال</option>
                        <option value="companies">عدد الشركات</option>
                        <option value="created">تاريخ الإنشاء</option>
                    </select>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <div>
                    <button class="btn btn-primary btn-sm" onclick="applyFilters()">
                        <i class="bi bi-search"></i>
                        تطبيق الفلاتر
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                        <i class="bi bi-x-circle"></i>
                        مسح الفلاتر
                    </button>
                </div>
                
                <div>
                    <button class="btn btn-outline-success btn-sm" onclick="exportData()">
                        <i class="bi bi-download"></i>
                        تصدير البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>



{% if categories %}
<div class="row">
    {% for category in categories %}
    <div class="col-md-6 col-lg-4 mb-4 fade-in-up" style="animation-delay: {{ loop.index * 0.1 }}s;">
        <div class="card h-100 shadow-sm border-0">
            <div class="card-header bg-gradient bg-light border-0 d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-bold">
                    <i class="bi bi-cash-coin text-success me-2"></i>
                    {{ category.name }}
                </h5>
                <div class="text-end">
                    <span class="badge bg-success fs-6 mb-1">{{ "{:,}".format(category.amount) }}</span>
                    <br><small class="text-muted">دينار ليبي</small>
                </div>
            </div>
            <div class="card-body">
                {% if category.description %}
                <p class="card-text text-muted">{{ category.description }}</p>
                {% endif %}
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-success mb-0">{{ category.current_companies_count }}</h4>
                            <small class="text-muted">الشركات الحالية</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-primary mb-0">{{ category.max_companies }}</h4>
                        <small class="text-muted">الحد الأقصى</small>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="mt-3">
                    {% set progress_percentage = (category.current_companies_count / category.max_companies * 100) if category.max_companies > 0 else 0 %}
                    <div class="progress">
                        <div class="progress-bar {% if progress_percentage >= 100 %}bg-danger{% elif progress_percentage >= 80 %}bg-warning{% else %}bg-success{% endif %}" 
                             role="progressbar" 
                             style="width: {{ progress_percentage }}%"
                             aria-valuenow="{{ category.current_companies_count }}" 
                             aria-valuemin="0" 
                             aria-valuemax="{{ category.max_companies }}">
                            {{ "%.0f"|format(progress_percentage) }}%
                        </div>
                    </div>
                    <small class="text-muted">معدل الإشغال</small>
                </div>
            </div>
            <div class="card-footer bg-transparent">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="action-buttons">
                        <a href="{{ url_for('category_companies', category_id=category.id) }}" 
                           class="btn btn-outline-info btn-sm" title="عرض الشركات">
                            <i class="bi bi-building me-1"></i>
                            عرض الشركات
                        </a>
                        
                        {% if category.can_add_company %}
                        <a href="{{ url_for('add_company', category_id=category.id) }}" 
                           class="btn btn-success btn-sm" title="إضافة شركة جديدة">
                            <i class="bi bi-plus me-1"></i>
                            إضافة شركة
                        </a>
                        {% else %}
                        <button class="btn btn-secondary btn-sm" disabled title="الفئة مكتملة">
                            <i class="bi bi-x-circle me-1"></i>
                            مكتملة
                        </button>
                        {% endif %}
                    </div>
                    
                    <div class="action-buttons">
                        <a href="{{ url_for('edit_capital_category', category_id=category.id) }}" 
                           class="btn btn-outline-warning btn-sm" title="تعديل الفئة">
                            <i class="bi bi-pencil-square"></i>
                        </a>
                        
                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                title="حذف الفئة"
                                onclick="confirmDelete('{{ category.name }}', '{{ url_for('delete_capital_category', category_id=category.id) }}')">
                            <i class="bi bi-trash3"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="text-center py-5 fade-in-up">
    <div class="mb-4">
        <i class="bi bi-cash-stack display-1 text-muted opacity-50"></i>
    </div>
    <h3 class="mt-3 text-muted">لا توجد فئات رأس مال</h3>
    <p class="text-muted mb-4">ابدأ بإضافة فئة رأس مال جديدة لتنظيم الشركات</p>
    <a href="{{ url_for('add_capital_category') }}" class="btn btn-primary btn-lg">
        <i class="bi bi-plus-circle me-2"></i>
        إضافة فئة جديدة
    </a>
</div>
{% endif %}

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    تأكيد الحذف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <i class="bi bi-trash3 display-4 text-danger"></i>
                </div>
                <h6 class="mb-3">هل أنت متأكد من حذف فئة رأس المال؟</h6>
                <p class="text-muted mb-0" id="deleteItemName"></p>
                <div class="alert alert-warning mt-3">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> سيتم حذف جميع البيانات المرتبطة بهذه الفئة نهائياً
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-2"></i>
                    إلغاء
                </button>
                <form id="deleteForm" method="POST" class="d-inline">
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash3 me-2"></i>
                        حذف نهائياً
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// بيانات الفئات للفلترة
const categoriesData = [
    {% for category in categories %}
    {
        id: {{ category.id }},
        name: "{{ category.name }}",
        amount: {{ category.amount }},
        maxCompanies: {{ category.max_companies }},
        currentCompanies: {{ category.current_companies_count }},
        usage: {{ (category.current_companies_count / category.max_companies * 100) if category.max_companies > 0 else 0 }},
        element: document.querySelector('[data-category-id="{{ category.id }}"]')
    },
    {% endfor %}
];

// تحديث الإحصائيات
function updateStats() {
    const visibleCategories = categoriesData.filter(cat => 
        cat.element && !cat.element.closest('.col-md-6').classList.contains('d-none')
    );
    
    const totalCompanies = visibleCategories.reduce((sum, cat) => sum + cat.currentCompanies, 0);
    const availableSlots = visibleCategories.reduce((sum, cat) => sum + (cat.maxCompanies - cat.currentCompanies), 0);
    const averageUsage = visibleCategories.length > 0 
        ? visibleCategories.reduce((sum, cat) => sum + cat.usage, 0) / visibleCategories.length 
        : 0;
    
    document.getElementById('totalCompanies').textContent = totalCompanies;
    document.getElementById('availableSlots').textContent = availableSlots;
    document.getElementById('averageUsage').textContent = Math.round(averageUsage) + '%';
}

// تطبيق الفلاتر
function applyFilters() {
    const searchName = document.getElementById('searchName').value.toLowerCase();
    const filterAmount = document.getElementById('filterAmount').value;
    const filterUsage = document.getElementById('filterUsage').value;
    const sortBy = document.getElementById('sortBy').value;
    
    let filteredCategories = [...categoriesData];
    
    // فلتر البحث بالاسم
    if (searchName) {
        filteredCategories = filteredCategories.filter(cat => 
            cat.name.toLowerCase().includes(searchName)
        );
    }
    
    // فلتر المبلغ
    if (filterAmount) {
        filteredCategories = filteredCategories.filter(cat => {
            if (filterAmount === '0-25000') return cat.amount < 25000;
            if (filterAmount === '25000-100000') return cat.amount >= 25000 && cat.amount <= 100000;
            if (filterAmount === '100000-500000') return cat.amount > 100000 && cat.amount <= 500000;
            if (filterAmount === '500000+') return cat.amount > 500000;
            return true;
        });
    }
    
    // فلتر معدل الإشغال
    if (filterUsage) {
        filteredCategories = filteredCategories.filter(cat => {
            if (filterUsage === 'empty') return cat.usage === 0;
            if (filterUsage === 'low') return cat.usage > 0 && cat.usage <= 50;
            if (filterUsage === 'medium') return cat.usage > 50 && cat.usage <= 80;
            if (filterUsage === 'high') return cat.usage > 80 && cat.usage < 100;
            if (filterUsage === 'full') return cat.usage === 100;
            return true;
        });
    }
    
    // ترتيب النتائج
    filteredCategories.sort((a, b) => {
        switch (sortBy) {
            case 'name': return a.name.localeCompare(b.name, 'ar');
            case 'amount': return b.amount - a.amount;
            case 'usage': return b.usage - a.usage;
            case 'companies': return b.currentCompanies - a.currentCompanies;
            default: return 0;
        }
    });
    
    // إخفاء/إظهار العناصر
    categoriesData.forEach(cat => {
        const container = cat.element ? cat.element.closest('.col-md-6') : null;
        if (container) {
            if (filteredCategories.includes(cat)) {
                container.classList.remove('d-none');
            } else {
                container.classList.add('d-none');
            }
        }
    });
    
    // إعادة ترتيب العناصر المرئية
    const container = document.querySelector('.row');
    const visibleElements = filteredCategories
        .map(cat => cat.element ? cat.element.closest('.col-md-6') : null)
        .filter(el => el !== null);
    
    visibleElements.forEach(el => container.appendChild(el));
    
    updateStats();
    
    // إظهار رسالة إذا لم توجد نتائج
    const noResults = document.getElementById('noResults');
    if (filteredCategories.length === 0) {
        if (!noResults) {
            const msg = document.createElement('div');
            msg.id = 'noResults';
            msg.className = 'col-12 text-center py-5';
            msg.innerHTML = `
                <i class="bi bi-search display-1 text-muted"></i>
                <h3 class="mt-3 text-muted">لا توجد نتائج</h3>
                <p class="text-muted">جرب تغيير معايير البحث</p>
            `;
            container.appendChild(msg);
        }
    } else if (noResults) {
        noResults.remove();
    }
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchName').value = '';
    document.getElementById('filterAmount').value = '';
    document.getElementById('filterUsage').value = '';
    document.getElementById('sortBy').value = 'name';
    
    // إظهار جميع العناصر
    categoriesData.forEach(cat => {
        const container = cat.element ? cat.element.closest('.col-md-6') : null;
        if (container) {
            container.classList.remove('d-none');
        }
    });
    
    // إزالة رسالة عدم وجود نتائج
    const noResults = document.getElementById('noResults');
    if (noResults) noResults.remove();
    
    updateStats();
}

// تصدير البيانات
function exportData() {
    const data = categoriesData.map(cat => ({
        'اسم الفئة': cat.name,
        'المبلغ (دينار)': cat.amount.toLocaleString(),
        'الحد الأقصى للشركات': cat.maxCompanies,
        'الشركات الحالية': cat.currentCompanies,
        'معدل الإشغال': Math.round(cat.usage) + '%',
        'الأماكن المتاحة': cat.maxCompanies - cat.currentCompanies
    }));
    
    // تحويل إلى CSV
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');
    
    // تحميل الملف
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `فئات_رأس_المال_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    
    showAlert('تم تصدير البيانات بنجاح', 'success');
}

// البحث المباشر
document.getElementById('searchName').addEventListener('input', function() {
    if (this.value.length === 0 || this.value.length >= 2) {
        applyFilters();
    }
});

// تطبيق الفلاتر عند التغيير
document.getElementById('filterAmount').addEventListener('change', applyFilters);
document.getElementById('filterUsage').addEventListener('change', applyFilters);
document.getElementById('sortBy').addEventListener('change', applyFilters);

// إضافة data attributes للعناصر
document.addEventListener('DOMContentLoaded', function() {
    {% for category in categories %}
    const element{{ category.id }} = document.querySelector('.card:has([href*="category_id={{ category.id }}"])');
    if (element{{ category.id }}) {
        element{{ category.id }}.setAttribute('data-category-id', '{{ category.id }}');
    }
    {% endfor %}
    
    // تحديث الإحصائيات الأولية
    updateStats();
});

// دالة تأكيد الحذف المحسنة
function confirmDelete(itemName, deleteUrl) {
    document.getElementById('deleteItemName').textContent = itemName;
    document.getElementById('deleteForm').action = deleteUrl;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// دالة عرض التنبيهات
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}