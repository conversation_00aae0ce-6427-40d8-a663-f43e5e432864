{% extends "base.html" %}

{% block title %}تعديل الشركة - {{ company.name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-pencil-square"></i>
        تعديل الشركة
    </h2>
    <div class="action-buttons">
        <a href="{{ url_for('company_details', company_id=company.id) }}" class="btn btn-outline-info">
            <i class="bi bi-eye"></i>
            عرض التفاصيل
        </a>
        <a href="{{ url_for('category_companies', category_id=company.capital_category_id) }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للفئة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-building"></i>
                    معلومات الشركة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الشركة *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ company.name }}" required>
                            <div class="form-text">اسم الشركة كما سيظهر في النظام</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">فئة رأس المال</label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-primary fs-6">{{ company.capital_category.name }}</span>
                            </div>
                            <div class="form-text">لا يمكن تغيير فئة رأس المال بعد الإنشاء</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="project_plan" class="form-label">خطة المشروع *</label>
                        <textarea class="form-control" id="project_plan" name="project_plan" 
                                  rows="4" required>{{ company.project_plan }}</textarea>
                        <div class="form-text">وصف مفصل لخطة المشروع والأهداف</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="resources" class="form-label">الموارد</label>
                            <textarea class="form-control" id="resources" name="resources" 
                                      rows="3">{{ company.resources or '' }}</textarea>
                            <div class="form-text">الموارد المتاحة للمشروع</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="work_method" class="form-label">طريقة العمل</label>
                            <textarea class="form-control" id="work_method" name="work_method" 
                                      rows="3">{{ company.work_method or '' }}</textarea>
                            <div class="form-text">منهجية وطريقة تنفيذ المشروع</div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label">الشركاء</label>
                        <div class="row">
                            {% for person in people %}
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           name="partners" value="{{ person.id }}" id="partner_{{ person.id }}"
                                           {% if person in company.partners %}checked{% endif %}>
                                    <label class="form-check-label" for="partner_{{ person.id }}">
                                        {{ person.name }}
                                        {% if person.phone %}
                                        <small class="text-muted d-block">{{ person.phone }}</small>
                                        {% endif %}
                                    </label>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="form-text">اختر الأشخاص الذين سيكونون شركاء في هذه الشركة</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i>
                            حفظ التغييرات
                        </button>
                        
                        <a href="{{ url_for('company_details', company_id=company.id) }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- معلومات الشركة -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات الشركة
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>تاريخ الإنشاء:</strong>
                    <p class="text-muted">{{ company.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                </div>
                
                <div class="mb-3">
                    <strong>فئة رأس المال:</strong>
                    <p class="text-muted">{{ company.capital_category.name }}</p>
                    <p class="text-muted small">{{ "{:,}".format(company.capital_category.amount) }} دينار</p>
                </div>
                
                <div class="mb-3">
                    <strong>عدد الملفات:</strong>
                    <p class="text-muted">{{ company.files|length }} ملف</p>
                </div>
                
                <div class="mb-3">
                    <strong>عدد الشركاء الحاليين:</strong>
                    <p class="text-muted">{{ company.partners|length }} شريك</p>
                </div>
                
                <div class="mb-3">
                    <strong>الحالة:</strong>
                    <p>
                        {% if company.is_active %}
                        <span class="badge bg-success">نشطة</span>
                        {% else %}
                        <span class="badge bg-danger">غير نشطة</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
        
        <!-- إرشادات التعديل -->
        <div class="card mt-3">
            <div class="card-header bg-info">
                <h6 class="mb-0 text-white">
                    <i class="bi bi-lightbulb"></i>
                    إرشادات التعديل
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        يمكن تعديل جميع المعلومات عدا فئة رأس المال
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-people text-primary"></i>
                        يمكن إضافة أو إزالة الشركاء في أي وقت
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-file-text text-info"></i>
                        الملفات المرفوعة ستبقى كما هي
                    </li>
                    <li>
                        <i class="bi bi-clock text-warning"></i>
                        سيتم حفظ تاريخ آخر تعديل تلقائياً
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- إجراءات سريعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-lightning"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('company_details', company_id=company.id) }}" class="btn btn-outline-info btn-sm">
                        <i class="bi bi-eye"></i>
                        عرض التفاصيل الكاملة
                    </a>
                    
                    <a href="#" class="btn btn-outline-success btn-sm">
                        <i class="bi bi-file-plus"></i>
                        رفع ملف جديد
                    </a>
                    
                    <hr>
                    
                    <form method="POST" action="{{ url_for('delete_company', company_id=company.id) }}" 
                          onsubmit="return confirmDelete('هل أنت متأكد من حذف شركة &quot;{{ company.name }}&quot;؟ هذا الإجراء لا يمكن التراجع عنه.')">
                        <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                            <i class="bi bi-trash"></i>
                            حذف الشركة
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// عداد الأحرف للنصوص الطويلة
function setupCharacterCounter(textareaId, maxLength = 1000) {
    const textarea = document.getElementById(textareaId);
    if (!textarea) return;
    
    const counter = document.createElement('div');
    counter.className = 'form-text text-end';
    counter.style.fontSize = '0.8rem';
    textarea.parentNode.appendChild(counter);
    
    function updateCounter() {
        const remaining = maxLength - textarea.value.length;
        counter.textContent = `${textarea.value.length}/${maxLength} حرف`;
        counter.className = remaining < 50 ? 'form-text text-end text-danger' : 'form-text text-end text-muted';
    }
    
    textarea.addEventListener('input', updateCounter);
    updateCounter();
}

// تطبيق عداد الأحرف
document.addEventListener('DOMContentLoaded', function() {
    setupCharacterCounter('project_plan', 2000);
    setupCharacterCounter('resources', 1000);
    setupCharacterCounter('work_method', 1000);
});

// تحديد/إلغاء تحديد جميع الشركاء
function toggleAllPartners() {
    const checkboxes = document.querySelectorAll('input[name="partners"]');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    
    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
    });
}

// إضافة زر تحديد الكل
document.addEventListener('DOMContentLoaded', function() {
    const partnersLabel = document.querySelector('label[class="form-label"]:has-text("الشركاء")');
    if (partnersLabel) {
        const toggleBtn = document.createElement('button');
        toggleBtn.type = 'button';
        toggleBtn.className = 'btn btn-outline-secondary btn-sm ms-2';
        toggleBtn.innerHTML = '<i class="bi bi-check-all"></i> تحديد الكل';
        toggleBtn.onclick = toggleAllPartners;
        partnersLabel.appendChild(toggleBtn);
    }
});
</script>
{% endblock %}