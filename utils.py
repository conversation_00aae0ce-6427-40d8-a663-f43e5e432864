#!/usr/bin/env python3
"""
أدوات مساعدة للنظام
Utility functions for the Company Management System
"""

import os
import re
import hashlib
from datetime import datetime
from pathlib import Path
from PIL import Image
import mimetypes

def validate_saudi_national_id(national_id):
    """التحقق من صحة رقم الهوية السعودية"""
    if not national_id or len(national_id) != 10:
        return False
    
    if not national_id.isdigit():
        return False
    
    # يجب أن يبدأ بـ 1 أو 2
    if not national_id.startswith(('1', '2')):
        return False
    
    # خوارزمية التحقق من رقم الهوية السعودية
    digits = [int(d) for d in national_id]
    
    # ضرب الأرقام في المواضع الفردية في 2
    for i in range(0, 9, 2):
        digits[i] *= 2
        if digits[i] > 9:
            digits[i] = digits[i] // 10 + digits[i] % 10
    
    # جمع جميع الأرقام
    total = sum(digits[:-1])
    
    # حساب رقم التحقق
    check_digit = (10 - (total % 10)) % 10
    
    return check_digit == digits[-1]

def validate_saudi_phone(phone):
    """التحقق من صحة رقم الهاتف السعودي"""
    if not phone:
        return False
    
    # إزالة المسافات والرموز
    phone = re.sub(r'[^\d]', '', phone)
    
    # يجب أن يكون 10 أرقام ويبدأ بـ 05
    if len(phone) == 10 and phone.startswith('05'):
        return True
    
    # أو 12 رقم ويبدأ بـ 9665
    if len(phone) == 12 and phone.startswith('9665'):
        return True
    
    return False

def format_saudi_phone(phone):
    """تنسيق رقم الهاتف السعودي"""
    if not phone:
        return phone
    
    phone = re.sub(r'[^\d]', '', phone)
    
    if len(phone) == 10 and phone.startswith('05'):
        return phone
    elif len(phone) == 9 and phone.startswith('5'):
        return '0' + phone
    elif len(phone) == 12 and phone.startswith('9665'):
        return '0' + phone[3:]
    
    return phone

def validate_email(email):
    """التحقق من صحة البريد الإلكتروني"""
    if not email:
        return True  # البريد الإلكتروني اختياري
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def generate_file_hash(file_path):
    """إنشاء hash للملف للتحقق من التكرار"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def get_file_info(file_path):
    """الحصول على معلومات الملف"""
    if not os.path.exists(file_path):
        return None
    
    stat = os.stat(file_path)
    mime_type, _ = mimetypes.guess_type(file_path)
    
    return {
        'size': stat.st_size,
        'created': datetime.fromtimestamp(stat.st_ctime),
        'modified': datetime.fromtimestamp(stat.st_mtime),
        'mime_type': mime_type,
        'extension': Path(file_path).suffix.lower()
    }

def resize_image(image_path, max_width=1920, max_height=1080, quality=85):
    """تصغير حجم الصورة"""
    try:
        with Image.open(image_path) as img:
            # التحقق من حاجة الصورة للتصغير
            if img.width <= max_width and img.height <= max_height:
                return False
            
            # حساب الأبعاد الجديدة مع الحفاظ على النسبة
            ratio = min(max_width / img.width, max_height / img.height)
            new_width = int(img.width * ratio)
            new_height = int(img.height * ratio)
            
            # تصغير الصورة
            img_resized = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # حفظ الصورة المصغرة
            if img.format == 'JPEG':
                img_resized.save(image_path, 'JPEG', quality=quality, optimize=True)
            else:
                img_resized.save(image_path, img.format, optimize=True)
            
            return True
    except Exception as e:
        print(f"خطأ في تصغير الصورة: {e}")
        return False

def format_file_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def sanitize_filename(filename):
    """تنظيف اسم الملف من الأحرف غير المسموحة"""
    # إزالة الأحرف الخطيرة
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # إزالة المسافات الزائدة
    filename = re.sub(r'\s+', ' ', filename).strip()
    
    # التأكد من عدم تجاوز الطول المسموح
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename

def generate_unique_filename(original_filename, upload_dir):
    """إنشاء اسم ملف فريد"""
    filename = sanitize_filename(original_filename)
    name, ext = os.path.splitext(filename)
    
    counter = 1
    new_filename = filename
    
    while os.path.exists(os.path.join(upload_dir, new_filename)):
        new_filename = f"{name}_{counter}{ext}"
        counter += 1
    
    return new_filename

def calculate_directory_size(directory):
    """حساب حجم المجلد"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(directory):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            if os.path.exists(filepath):
                total_size += os.path.getsize(filepath)
    return total_size

def clean_old_files(directory, days_old=30):
    """حذف الملفات القديمة"""
    if not os.path.exists(directory):
        return 0
    
    cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
    deleted_count = 0
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            if os.path.getmtime(file_path) < cutoff_time:
                try:
                    os.remove(file_path)
                    deleted_count += 1
                except OSError:
                    pass
    
    return deleted_count

def format_arabic_date(date_obj, include_time=False):
    """تنسيق التاريخ بالعربية"""
    if not date_obj:
        return ""
    
    months_ar = [
        "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
        "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
    ]
    
    day = date_obj.day
    month = months_ar[date_obj.month - 1]
    year = date_obj.year
    
    formatted_date = f"{day} {month} {year}"
    
    if include_time:
        hour = date_obj.hour
        minute = date_obj.minute
        formatted_date += f" - {hour:02d}:{minute:02d}"
    
    return formatted_date

def convert_to_arabic_numbers(text):
    """تحويل الأرقام الإنجليزية إلى عربية"""
    arabic_digits = '٠١٢٣٤٥٦٧٨٩'
    english_digits = '0123456789'
    
    for i, digit in enumerate(english_digits):
        text = text.replace(digit, arabic_digits[i])
    
    return text

def validate_company_name(name):
    """التحقق من صحة اسم الشركة"""
    if not name or len(name.strip()) < 3:
        return False, "اسم الشركة يجب أن يكون 3 أحرف على الأقل"
    
    if len(name) > 200:
        return False, "اسم الشركة طويل جداً (الحد الأقصى 200 حرف)"
    
    # التحقق من عدم وجود أحرف خطيرة
    dangerous_chars = ['<', '>', '"', "'", '&', ';']
    if any(char in name for char in dangerous_chars):
        return False, "اسم الشركة يحتوي على أحرف غير مسموحة"
    
    return True, ""

def log_activity(user_id, action, details=""):
    """تسجيل نشاط المستخدم (للتطوير المستقبلي)"""
    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'user_id': user_id,
        'action': action,
        'details': details
    }
    
    # يمكن حفظ هذا في قاعدة البيانات أو ملف log
    print(f"LOG: {log_entry}")

def get_system_stats():
    """إحصائيات سريعة للنظام"""
    current_dir = Path(__file__).parent
    
    stats = {
        'timestamp': datetime.now().isoformat(),
        'database_exists': (current_dir / 'companies.db').exists(),
        'uploads_dir_exists': (current_dir / 'uploads').exists(),
        'backups_dir_exists': (current_dir / 'backups').exists(),
    }
    
    # حساب أحجام المجلدات
    if stats['uploads_dir_exists']:
        stats['uploads_size'] = calculate_directory_size(current_dir / 'uploads')
    
    if stats['backups_dir_exists']:
        stats['backups_size'] = calculate_directory_size(current_dir / 'backups')
    
    if stats['database_exists']:
        stats['database_size'] = (current_dir / 'companies.db').stat().st_size
    
    return stats