{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة الشركات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 fade-in-up">
    <h2>
        <i class="bi bi-speedometer2 text-primary"></i>
        لوحة التحكم
    </h2>
    <div class="text-muted">
        <i class="bi bi-person-circle"></i>
        مرحباً، <strong>{{ current_user.username }}</strong>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3 fade-in-up" style="animation-delay: 0.1s;">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="bi bi-cash-stack display-4"></i>
                </div>
                <h3 class="mb-2">{{ total_categories }}</h3>
                <p class="mb-0 opacity-75">فئات رأس المال</p>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('capital_categories') }}" class="text-white text-decoration-none d-flex align-items-center justify-content-center">
                    <small>عرض التفاصيل</small>
                    <i class="bi bi-arrow-left ms-2"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3 fade-in-up" style="animation-delay: 0.2s;">
        <div class="card stats-card stats-card-success">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="bi bi-building display-4"></i>
                </div>
                <h3 class="mb-2">{{ total_companies }}</h3>
                <p class="mb-0 opacity-75">الشركات النشطة</p>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('capital_categories') }}" class="text-white text-decoration-none d-flex align-items-center justify-content-center">
                    <small>عرض الشركات</small>
                    <i class="bi bi-arrow-left ms-2"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3 fade-in-up" style="animation-delay: 0.3s;">
        <div class="card stats-card stats-card-info">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="bi bi-people display-4"></i>
                </div>
                <h3 class="mb-2">{{ total_people }}</h3>
                <p class="mb-0 opacity-75">الأشخاص المسجلين</p>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('people') }}" class="text-white text-decoration-none d-flex align-items-center justify-content-center">
                    <small>إدارة الأشخاص</small>
                    <i class="bi bi-arrow-left ms-2"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3 fade-in-up" style="animation-delay: 0.4s;">
        <div class="card stats-card stats-card-warning">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="bi bi-shield-check display-4"></i>
                </div>
                <h3 class="mb-2">{{ available_slots }}</h3>
                <p class="mb-0 opacity-75">الأماكن المتاحة</p>
            </div>
            <div class="card-footer bg-transparent border-0">
                <small class="text-white opacity-75">في جميع الفئات</small>
            </div>
        </div>
    </div>
</div>

<!-- Recent Companies -->
<div class="row">
    <div class="col-md-8">
        <div class="card slide-in-right" style="animation-delay: 0.5s;">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-building text-primary"></i>
                    أحدث الشركات المضافة
                </h5>
                <a href="{{ url_for('capital_categories') }}" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-eye"></i>
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_companies %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم الشركة</th>
                                <th>فئة رأس المال</th>
                                <th>المالك</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for company in recent_companies %}
                            <tr>
                                <td>
                                    <strong>{{ company.name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ company.capital_category.name }}</span>
                                </td>
                                <td>{{ company.primary_owner.name }}</td>
                                <td>
                                    <small class="text-muted">
                                        {{ company.created_at.strftime('%Y-%m-%d') }}
                                    </small>
                                </td>
                                <td>
                                    <a href="{{ url_for('company_details', company_id=company.id) }}" 
                                       class="btn btn-sm btn-outline-info">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="bi bi-building display-4"></i>
                    <p class="mt-2">لا توجد شركات مضافة بعد</p>
                    <a href="{{ url_for('capital_categories') }}" class="btn btn-primary">
                        إضافة شركة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card fade-in-up" style="animation-delay: 0.6s;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning text-warning"></i>
                    الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{{ url_for('add_capital_category') }}" class="btn btn-outline-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة فئة رأس مال جديدة
                    </a>
                    <a href="{{ url_for('add_person') }}" class="btn btn-outline-success">
                        <i class="bi bi-person-plus me-2"></i>
                        إضافة شخص جديد
                    </a>
                    <a href="{{ url_for('capital_categories') }}" class="btn btn-outline-info">
                        <i class="bi bi-building-add me-2"></i>
                        إضافة شركة جديدة
                    </a>
                    
                    <hr class="my-3">
                    
                    <div class="text-center">
                        <h6 class="text-muted mb-3">
                            <i class="bi bi-graph-up"></i>
                            إحصائيات سريعة
                        </h6>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border rounded p-2 mb-2">
                                    <strong class="text-primary">{{ (total_companies / total_categories * 100) | round(1) if total_categories > 0 else 0 }}%</strong>
                                    <small class="d-block text-muted">معدل الإشغال</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-2 mb-2">
                                    <strong class="text-success">{{ total_categories + total_companies + total_people }}</strong>
                                    <small class="d-block text-muted">إجمالي السجلات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات إضافية -->
        <div class="card mt-3 fade-in-up" style="animation-delay: 0.7s;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-tools text-info"></i>
                    أدوات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-info btn-sm" onclick="generateReport()">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        تقرير شامل
                    </button>
                    
                    <button class="btn btn-outline-success btn-sm" onclick="exportAllData()">
                        <i class="bi bi-download me-2"></i>
                        تصدير جميع البيانات
                    </button>
                    
                    <button class="btn btn-outline-warning btn-sm" onclick="showSystemStats()">
                        <i class="bi bi-graph-up me-2"></i>
                        إحصائيات متقدمة
                    </button>
                    
                    <hr>
                    
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshDashboard()">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        تحديث البيانات
                    </button>
                </div>
            </div>
        </div>
        
        <!-- معلومات النظام -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <strong>الإصدار:</strong> 1.0.0<br>
                    <strong>تاريخ اليوم:</strong> {{ current_date.strftime('%Y-%m-%d') }}<br>
                    <strong>المستخدم:</strong> {{ current_user.username }}<br>
                    <strong>آخر تحديث:</strong> <span id="lastUpdate">{{ current_date.strftime('%H:%M') }}</span>
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Modal للإحصائيات المتقدمة -->
<div class="modal fade" id="statsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-graph-up"></i>
                    إحصائيات متقدمة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="statsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للتقرير الشامل -->
<div class="modal fade" id="reportModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-file-earmark-text"></i>
                    التقرير الشامل
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="reportContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري إنشاء التقرير...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" onclick="printReport()">
                    <i class="bi bi-printer"></i>
                    طباعة
                </button>
                <button type="button" class="btn btn-primary" onclick="downloadReport()">
                    <i class="bi bi-download"></i>
                    تحميل PDF
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تحديث لوحة التحكم
async function refreshDashboard() {
    try {
        const response = await fetch('/api/stats');
        const data = await response.json();
        
        // تحديث الإحصائيات
        document.querySelector('.card-body h2:contains("{{ total_categories }}")').textContent = data.total_categories;
        document.querySelector('.card-body h2:contains("{{ total_companies }}")').textContent = data.total_companies;
        document.querySelector('.card-body h2:contains("{{ total_people }}")').textContent = data.total_people;
        
        // تحديث وقت آخر تحديث
        document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('ar-SA');
        
        showAlert('تم تحديث البيانات بنجاح', 'success');
    } catch (error) {
        showAlert('حدث خطأ أثناء تحديث البيانات', 'error');
    }
}

// إظهار الإحصائيات المتقدمة
async function showSystemStats() {
    const modal = new bootstrap.Modal(document.getElementById('statsModal'));
    modal.show();
    
    try {
        const response = await fetch('/api/stats');
        const data = await response.json();
        
        const content = `
            <div class="row">
                <div class="col-md-6">
                    <h6>إحصائيات الفئات</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الفئة</th>
                                    <th>الإشغال</th>
                                    <th>المتاح</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.categories_usage.map(cat => `
                                    <tr>
                                        <td>${cat.name}</td>
                                        <td>
                                            <div class="progress" style="height: 15px;">
                                                <div class="progress-bar ${cat.percentage >= 100 ? 'bg-danger' : cat.percentage >= 80 ? 'bg-warning' : 'bg-success'}" 
                                                     style="width: ${cat.percentage}%">
                                                    ${Math.round(cat.percentage)}%
                                                </div>
                                            </div>
                                        </td>
                                        <td>${cat.max - cat.current}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h6>إحصائيات عامة</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between">
                            <span>إجمالي رؤوس الأموال</span>
                            <strong>${data.categories_usage.reduce((sum, cat) => sum + (cat.amount || 0), 0).toLocaleString()} دينار</strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>متوسط الشركات لكل فئة</span>
                            <strong>${Math.round(data.total_companies / data.total_categories)} شركة</strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>معدل الإشغال العام</span>
                            <strong>${Math.round(data.categories_usage.reduce((sum, cat) => sum + cat.percentage, 0) / data.categories_usage.length)}%</strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>الفئات المكتملة</span>
                            <strong>${data.categories_usage.filter(cat => cat.percentage >= 100).length} فئة</strong>
                        </li>
                    </ul>
                </div>
            </div>
        `;
        
        document.getElementById('statsContent').innerHTML = content;
    } catch (error) {
        document.getElementById('statsContent').innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                حدث خطأ أثناء تحميل الإحصائيات
            </div>
        `;
    }
}

// إنشاء تقرير شامل
async function generateReport() {
    const modal = new bootstrap.Modal(document.getElementById('reportModal'));
    modal.show();
    
    try {
        const response = await fetch('/api/stats');
        const data = await response.json();
        
        const reportContent = `
            <div class="report-content">
                <div class="text-center mb-4">
                    <h3>تقرير شامل - نظام إدارة الشركات</h3>
                    <p class="text-muted">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-4 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-primary">${data.total_categories}</h4>
                            <p>فئات رأس المال</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-success">${data.total_companies}</h4>
                            <p>الشركات المسجلة</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-info">${data.total_people}</h4>
                            <p>الأشخاص المسجلين</p>
                        </div>
                    </div>
                </div>
                
                <h5>تفاصيل الفئات</h5>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم الفئة</th>
                                <th>رأس المال</th>
                                <th>الحد الأقصى</th>
                                <th>الشركات الحالية</th>
                                <th>معدل الإشغال</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.categories_usage.map(cat => `
                                <tr>
                                    <td>${cat.name}</td>
                                    <td>${(cat.amount || 0).toLocaleString()} دينار</td>
                                    <td>${cat.max}</td>
                                    <td>${cat.current}</td>
                                    <td>${Math.round(cat.percentage)}%</td>
                                    <td>
                                        <span class="badge ${cat.percentage >= 100 ? 'bg-danger' : cat.percentage >= 80 ? 'bg-warning' : 'bg-success'}">
                                            ${cat.percentage >= 100 ? 'مكتملة' : cat.percentage >= 80 ? 'شبه مكتملة' : 'متاحة'}
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-4">
                    <h6>ملاحظات:</h6>
                    <ul>
                        <li>هذا التقرير يعكس حالة النظام في تاريخ ${new Date().toLocaleDateString('ar-SA')}</li>
                        <li>يمكن تحديث البيانات من لوحة التحكم</li>
                        <li>للمزيد من التفاصيل، راجع الصفحات المخصصة لكل قسم</li>
                    </ul>
                </div>
            </div>
        `;
        
        document.getElementById('reportContent').innerHTML = reportContent;
    } catch (error) {
        document.getElementById('reportContent').innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                حدث خطأ أثناء إنشاء التقرير
            </div>
        `;
    }
}

// طباعة التقرير
function printReport() {
    const content = document.getElementById('reportContent').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>تقرير شامل - نظام إدارة الشركات</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class="container">
                    ${content}
                </div>
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// تحميل التقرير كـ PDF (يتطلب مكتبة إضافية)
function downloadReport() {
    showAlert('ميزة تحميل PDF ستكون متاحة قريباً', 'info');
}

// تصدير جميع البيانات
async function exportAllData() {
    try {
        const response = await fetch('/api/stats');
        const data = await response.json();
        
        // إنشاء ملف CSV شامل
        const csvData = [
            ['نوع البيانات', 'التفاصيل', 'القيمة'],
            ['إحصائيات عامة', 'إجمالي الفئات', data.total_categories],
            ['إحصائيات عامة', 'إجمالي الشركات', data.total_companies],
            ['إحصائيات عامة', 'إجمالي الأشخاص', data.total_people],
            ['', '', ''],
            ['تفاصيل الفئات', '', ''],
            ['اسم الفئة', 'الشركات الحالية', 'الحد الأقصى'],
            ...data.categories_usage.map(cat => [cat.name, cat.current, cat.max])
        ];
        
        const csvContent = csvData.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `تقرير_شامل_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
        
        showAlert('تم تصدير البيانات بنجاح', 'success');
    } catch (error) {
        showAlert('حدث خطأ أثناء تصدير البيانات', 'error');
    }
}

// تحديث تلقائي كل 5 دقائق
setInterval(refreshDashboard, 300000);
</script>
{% endblock %}