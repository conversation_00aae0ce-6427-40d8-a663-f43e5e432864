from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """نموذج المستخدم لتسجيل الدخول"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    is_admin = db.Column(db.<PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'<User {self.username}>'

class CapitalCategory(db.Model):
    """فئات رأس المال"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # مثل "10,000 دينار"
    amount = db.Column(db.Integer, nullable=False)  # المبلغ بالأرقام
    max_companies = db.Column(db.Integer, nullable=False, default=10)  # الحد الأقصى للشركات
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقة مع الشركات
    companies = db.relationship('Company', backref='capital_category', lazy=True)
    
    @property
    def current_companies_count(self):
        """عدد الشركات الحالية في هذه الفئة"""
        return Company.query.filter_by(capital_category_id=self.id, is_active=True).count()
    
    @property
    def can_add_company(self):
        """هل يمكن إضافة شركة جديدة لهذه الفئة"""
        return self.current_companies_count < self.max_companies
    
    def __repr__(self):
        return f'<CapitalCategory {self.name}>'

class Person(db.Model):
    """نموذج الأشخاص (الملاك)"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    national_id = db.Column(db.String(20), unique=True)  # رقم الهوية
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقة مع الشركات كمالك أساسي
    owned_companies = db.relationship('Company', backref='primary_owner', lazy=True)
    
    def __repr__(self):
        return f'<Person {self.name}>'

class Company(db.Model):
    """نموذج الشركات"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    project_plan = db.Column(db.Text, nullable=False)  # خطة المشروع
    resources = db.Column(db.Text)  # الموارد
    work_method = db.Column(db.Text)  # طريقة العمل
    
    # ربط مع فئة رأس المال
    capital_category_id = db.Column(db.Integer, db.ForeignKey('capital_category.id'), nullable=False)
    
    # المالك الأساسي
    primary_owner_id = db.Column(db.Integer, db.ForeignKey('person.id'), nullable=False)
    
    # معلومات إضافية
    registration_number = db.Column(db.String(50))  # رقم التسجيل
    license_number = db.Column(db.String(50))  # رقم الرخصة
    establishment_date = db.Column(db.Date)  # تاريخ التأسيس
    
    # حالة الشركة
    is_active = db.Column(db.Boolean, default=True)
    status = db.Column(db.String(50), default='active')  # active, suspended, closed
    
    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    files = db.relationship('CompanyFile', backref='company', lazy=True, cascade='all, delete-orphan')
    additional_owners = db.relationship('CompanyOwnership', backref='company', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Company {self.name}>'

class CompanyOwnership(db.Model):
    """جدول ربط الملاك الإضافيين بالشركات"""
    id = db.Column(db.Integer, primary_key=True)
    company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=False)
    person_id = db.Column(db.Integer, db.ForeignKey('person.id'), nullable=False)
    ownership_percentage = db.Column(db.Float, default=0.0)  # نسبة الملكية
    role = db.Column(db.String(100))  # الدور (شريك، مستثمر، إلخ)
    start_date = db.Column(db.Date, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    notes = db.Column(db.Text)
    
    # العلاقات
    person = db.relationship('Person', backref='company_ownerships')
    
    def __repr__(self):
        return f'<CompanyOwnership {self.company.name} - {self.person.name}>'

class CompanyFile(db.Model):
    """ملفات الشركات"""
    id = db.Column(db.Integer, primary_key=True)
    company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_type = db.Column(db.String(50))  # image, document, contract
    file_size = db.Column(db.Integer)  # بالبايت
    mime_type = db.Column(db.String(100))
    description = db.Column(db.Text)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    uploader = db.relationship('User', backref='uploaded_files')
    
    def __repr__(self):
        return f'<CompanyFile {self.original_filename}>'

class SystemBackup(db.Model):
    """نسخ احتياطية للنظام"""
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    backup_type = db.Column(db.String(50), default='manual')  # manual, automatic
    file_size = db.Column(db.Integer)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)
    
    # العلاقات
    creator = db.relationship('User', backref='created_backups')
    
    def __repr__(self):
        return f'<SystemBackup {self.filename}>'