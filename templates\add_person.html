{% extends "base.html" %}

{% block title %}إضافة شخص جديد - نظام إدارة الشركات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 fade-in-up">
    <div>
        <h2>
            <i class="bi bi-person-plus text-success"></i>
            إضافة شخص جديد
        </h2>
        <small class="text-muted">إضافة شخص جديد إلى قاعدة البيانات</small>
    </div>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('people') }}">الأشخاص</a></li>
            <li class="breadcrumb-item active">إضافة جديد</li>
        </ol>
    </nav>
</div>

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow-lg border-0 slide-in-right">
            <div class="card-header bg-gradient bg-success text-white py-3">
                <h4 class="mb-0 fw-bold">
                    <i class="bi bi-person-vcard me-2"></i>
                    بيانات الشخص الجديد
                </h4>
                <small class="opacity-75">يرجى ملء البيانات المطلوبة بدقة</small>
            </div>
            <div class="card-body p-4">
                <form method="POST" id="personForm">
                    <!-- المعلومات الأساسية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2 mb-3">
                                <i class="bi bi-person-circle me-2"></i>
                                المعلومات الأساسية
                            </h5>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label fw-bold">
                                <i class="bi bi-person text-success me-1"></i>
                                الاسم الكامل *
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-person"></i>
                                </span>
                                <input type="text" class="form-control" id="name" name="name" required
                                       placeholder="أدخل الاسم الكامل" autocomplete="name">
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                الاسم كما يظهر في الوثائق الرسمية
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="national_id" class="form-label fw-bold">
                                <i class="bi bi-card-text text-info me-1"></i>
                                رقم الهوية الوطنية
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-card-text"></i>
                                </span>
                                <input type="text" class="form-control" id="national_id" name="national_id"
                                       placeholder="1234567890123" maxlength="13" pattern="[0-9]{10,13}">
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                رقم الهوية الوطنية الليبية (اختياري)
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات الاتصال -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2 mb-3">
                                <i class="bi bi-telephone me-2"></i>
                                معلومات الاتصال
                            </h5>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label fw-bold">
                                <i class="bi bi-telephone text-success me-1"></i>
                                رقم الهاتف
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-telephone"></i>
                                </span>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       placeholder="0912345678" pattern="[0-9]{10}" dir="ltr">
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                رقم الهاتف المحمول (مثال: 0912345678)
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label fw-bold">
                                <i class="bi bi-envelope text-info me-1"></i>
                                البريد الإلكتروني
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-envelope"></i>
                                </span>
                                <input type="email" class="form-control" id="email" name="email"
                                       placeholder="<EMAIL>" dir="ltr">
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                البريد الإلكتروني للتواصل (اختياري)
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات إضافية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2 mb-3">
                                <i class="bi bi-geo-alt me-2"></i>
                                معلومات إضافية
                            </h5>
                        </div>
                        <div class="col-12 mb-3">
                            <label for="address" class="form-label fw-bold">
                                <i class="bi bi-geo-alt text-warning me-1"></i>
                                العنوان
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-geo-alt"></i>
                                </span>
                                <textarea class="form-control" id="address" name="address" rows="2"
                                          placeholder="العنوان الكامل (المدينة، المنطقة، الشارع...)"></textarea>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                العنوان السكني أو عنوان العمل
                            </div>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="notes" class="form-label fw-bold">
                                <i class="bi bi-sticky text-secondary me-1"></i>
                                ملاحظات
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-sticky"></i>
                                </span>
                                <textarea class="form-control" id="notes" name="notes" rows="3"
                                          placeholder="أي ملاحظات إضافية أو معلومات مهمة..."></textarea>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                ملاحظات خاصة أو معلومات إضافية مفيدة
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center bg-light rounded p-3">
                                <div class="text-muted">
                                    <i class="bi bi-info-circle me-2"></i>
                                    تأكد من صحة البيانات قبل الحفظ
                                </div>
                                <div class="action-buttons">
                                    <a href="{{ url_for('people') }}" class="btn btn-outline-secondary me-2">
                                        <i class="bi bi-x-circle me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="reset" class="btn btn-outline-warning me-2" onclick="clearForm()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>
                                        إعادة تعيين
                                    </button>
                                    <button type="submit" class="btn btn-success" id="submitBtn">
                                        <i class="bi bi-person-plus me-1"></i>
                                        إضافة الشخص
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Help Card -->
        <div class="card mt-4 border-0 shadow-sm fade-in-up" style="animation-delay: 0.3s;">
            <div class="card-header bg-gradient bg-info text-white">
                <h5 class="mb-0 fw-bold">
                    <i class="bi bi-lightbulb me-2"></i>
                    نصائح وإرشادات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">
                            <i class="bi bi-info-circle me-2"></i>
                            معلومات عامة
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2 d-flex align-items-start">
                                <i class="bi bi-check-circle text-success me-2 mt-1"></i>
                                <div>
                                    <strong>الاسم الكامل:</strong> مطلوب ويجب أن يكون كما يظهر في الوثائق الرسمية
                                </div>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="bi bi-check-circle text-success me-2 mt-1"></i>
                                <div>
                                    <strong>رقم الهوية:</strong> يُستخدم للتحقق من عدم تكرار الأشخاص في النظام
                                </div>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="bi bi-check-circle text-success me-2 mt-1"></i>
                                <div>
                                    <strong>المعلومات الاختيارية:</strong> يمكن إضافتها لاحقاً أو تركها فارغة
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning mb-3">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            تنبيهات مهمة
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2 d-flex align-items-start">
                                <i class="bi bi-arrow-right text-warning me-2 mt-1"></i>
                                <div>
                                    <strong>ربط الشركات:</strong> سيتم ربط الشخص تلقائياً عند إضافة شركة جديدة
                                </div>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="bi bi-arrow-right text-warning me-2 mt-1"></i>
                                <div>
                                    <strong>التعديل:</strong> يمكن تعديل جميع البيانات لاحقاً من قائمة الأشخاص
                                </div>
                            </li>
                            <li class="mb-0 d-flex align-items-start">
                                <i class="bi bi-arrow-right text-warning me-2 mt-1"></i>
                                <div>
                                    <strong>الحذف:</strong> احذر من حذف الأشخاص المرتبطين بشركات نشطة
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-light border-start border-primary border-4 mt-3">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-info-circle text-primary me-2"></i>
                        <div>
                            <strong>ملاحظة:</strong> جميع البيانات المدخلة محمية ومشفرة وفقاً لمعايير الأمان المعتمدة في ليبيا.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('personForm');
    const submitBtn = document.getElementById('submitBtn');
    const nameInput = document.getElementById('name');
    const nationalIdInput = document.getElementById('national_id');
    const phoneInput = document.getElementById('phone');
    const emailInput = document.getElementById('email');

    // تحسين تجربة المستخدم للحقول
    function setupFieldEnhancements() {
        // تنسيق رقم الهوية
        if (nationalIdInput) {
            nationalIdInput.addEventListener('input', function() {
                this.value = this.value.replace(/[^0-9]/g, '');
                if (this.value.length > 13) {
                    this.value = this.value.slice(0, 13);
                }
                validateNationalId(this.value);
            });
        }

        // تنسيق رقم الهاتف
        if (phoneInput) {
            phoneInput.addEventListener('input', function() {
                this.value = this.value.replace(/[^0-9]/g, '');
                if (this.value.length > 10) {
                    this.value = this.value.slice(0, 10);
                }
                validatePhone(this.value);
            });
        }

        // تحقق من البريد الإلكتروني
        if (emailInput) {
            emailInput.addEventListener('blur', function() {
                validateEmail(this.value);
            });
        }

        // تحسين حقل الاسم
        if (nameInput) {
            nameInput.addEventListener('input', function() {
                // إزالة الأرقام والرموز الخاصة
                this.value = this.value.replace(/[0-9!@#$%^&*()_+=\[\]{};':"\\|,.<>\/?]/g, '');
                validateName(this.value);
            });
        }
    }

    // التحقق من صحة رقم الهوية
    function validateNationalId(value) {
        const feedback = nationalIdInput.parentNode.parentNode.querySelector('.invalid-feedback') || 
                        createFeedbackElement(nationalIdInput.parentNode.parentNode);
        
        if (value && (value.length < 10 || value.length > 13)) {
            nationalIdInput.classList.add('is-invalid');
            nationalIdInput.classList.remove('is-valid');
            feedback.textContent = 'رقم الهوية يجب أن يكون بين 10 و 13 رقم';
            feedback.style.display = 'block';
        } else if (value) {
            nationalIdInput.classList.add('is-valid');
            nationalIdInput.classList.remove('is-invalid');
            feedback.style.display = 'none';
        } else {
            nationalIdInput.classList.remove('is-valid', 'is-invalid');
            feedback.style.display = 'none';
        }
    }

    // التحقق من صحة رقم الهاتف
    function validatePhone(value) {
        const feedback = phoneInput.parentNode.parentNode.querySelector('.invalid-feedback') || 
                        createFeedbackElement(phoneInput.parentNode.parentNode);
        
        if (value && value.length !== 10) {
            phoneInput.classList.add('is-invalid');
            phoneInput.classList.remove('is-valid');
            feedback.textContent = 'رقم الهاتف يجب أن يكون 10 أرقام';
            feedback.style.display = 'block';
        } else if (value && !value.startsWith('09')) {
            phoneInput.classList.add('is-invalid');
            phoneInput.classList.remove('is-valid');
            feedback.textContent = 'رقم الهاتف يجب أن يبدأ بـ 09';
            feedback.style.display = 'block';
        } else if (value) {
            phoneInput.classList.add('is-valid');
            phoneInput.classList.remove('is-invalid');
            feedback.style.display = 'none';
        } else {
            phoneInput.classList.remove('is-valid', 'is-invalid');
            feedback.style.display = 'none';
        }
    }

    // التحقق من صحة البريد الإلكتروني
    function validateEmail(value) {
        const feedback = emailInput.parentNode.parentNode.querySelector('.invalid-feedback') || 
                        createFeedbackElement(emailInput.parentNode.parentNode);
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (value && !emailRegex.test(value)) {
            emailInput.classList.add('is-invalid');
            emailInput.classList.remove('is-valid');
            feedback.textContent = 'البريد الإلكتروني غير صحيح';
            feedback.style.display = 'block';
        } else if (value) {
            emailInput.classList.add('is-valid');
            emailInput.classList.remove('is-invalid');
            feedback.style.display = 'none';
        } else {
            emailInput.classList.remove('is-valid', 'is-invalid');
            feedback.style.display = 'none';
        }
    }

    // التحقق من صحة الاسم
    function validateName(value) {
        const feedback = nameInput.parentNode.parentNode.querySelector('.invalid-feedback') || 
                        createFeedbackElement(nameInput.parentNode.parentNode);
        
        if (value && value.trim().length < 2) {
            nameInput.classList.add('is-invalid');
            nameInput.classList.remove('is-valid');
            feedback.textContent = 'الاسم يجب أن يكون حرفين على الأقل';
            feedback.style.display = 'block';
        } else if (value && value.trim().length > 100) {
            nameInput.classList.add('is-invalid');
            nameInput.classList.remove('is-valid');
            feedback.textContent = 'الاسم طويل جداً';
            feedback.style.display = 'block';
        } else if (value && value.trim().length >= 2) {
            nameInput.classList.add('is-valid');
            nameInput.classList.remove('is-invalid');
            feedback.style.display = 'none';
        } else {
            nameInput.classList.remove('is-valid', 'is-invalid');
            feedback.style.display = 'none';
        }
    }

    // إنشاء عنصر رسالة التحقق
    function createFeedbackElement(parent) {
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.style.display = 'none';
        parent.appendChild(feedback);
        return feedback;
    }

    // دالة إعادة تعيين النموذج
    window.clearForm = function() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الحقول؟')) {
            form.reset();
            // إزالة جميع فئات التحقق
            const inputs = form.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.classList.remove('is-valid', 'is-invalid');
            });
            // إخفاء رسائل التحقق
            const feedbacks = form.querySelectorAll('.invalid-feedback');
            feedbacks.forEach(feedback => {
                feedback.style.display = 'none';
            });
            nameInput.focus();
        }
    };

    // التحقق من النموذج قبل الإرسال
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // التحقق من الاسم
        if (!nameInput.value.trim()) {
            isValid = false;
            nameInput.classList.add('is-invalid');
            showAlert('يرجى إدخال الاسم الكامل', 'error');
        }

        // التحقق من رقم الهوية إذا تم إدخاله
        if (nationalIdInput.value && (nationalIdInput.value.length < 10 || nationalIdInput.value.length > 13)) {
            isValid = false;
            nationalIdInput.classList.add('is-invalid');
            showAlert('رقم الهوية غير صحيح', 'error');
        }

        // التحقق من رقم الهاتف إذا تم إدخاله
        if (phoneInput.value && (phoneInput.value.length !== 10 || !phoneInput.value.startsWith('09'))) {
            isValid = false;
            phoneInput.classList.add('is-invalid');
            showAlert('رقم الهاتف غير صحيح', 'error');
        }

        // التحقق من البريد الإلكتروني إذا تم إدخاله
        if (emailInput.value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailInput.value)) {
            isValid = false;
            emailInput.classList.add('is-invalid');
            showAlert('البريد الإلكتروني غير صحيح', 'error');
        }

        if (!isValid) {
            e.preventDefault();
            return false;
        }

        // تعطيل الزر لمنع الإرسال المتكرر
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> جاري الحفظ...';
        
        // إعادة تفعيل الزر بعد 3 ثوان في حالة عدم نجاح الإرسال
        setTimeout(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="bi bi-person-plus me-1"></i> إضافة الشخص';
        }, 3000);
    });

    // دالة عرض التنبيهات
    function showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // تهيئة التحسينات
    setupFieldEnhancements();
    
    // التركيز على حقل الاسم عند تحميل الصفحة
    if (nameInput) {
        nameInput.focus();
    }
});
</script>
{% endblock %}