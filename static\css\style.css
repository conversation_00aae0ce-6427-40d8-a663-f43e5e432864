/* Custom CSS for Company Management System */

/* RTL Support */
body {
    direction: rtl;
    text-align: right;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Custom Colors */
:root {
    --primary-color: #0d6efd;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --secondary-color: #6c757d;
    --light-bg: #f8f9fa;
    --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --card-shadow-hover: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

/* Navigation */
.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

/* Sidebar */
.sidebar {
    background: linear-gradient(180deg, #343a40 0%, #495057 100%);
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: #adb5bd;
    padding: 0.75rem 1.25rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255,255,255,0.1);
    transform: translateX(-2px);
}

.sidebar .nav-link.active {
    color: #fff;
    background-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.sidebar .nav-link i {
    margin-left: 0.5rem;
    width: 1.25rem;
    text-align: center;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Statistics Cards */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
    color: white;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.4);
}

.stats-card-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #146c43 100%);
    box-shadow: 0 4px 15px rgba(25, 135, 84, 0.3);
}

.stats-card-success:hover {
    box-shadow: 0 8px 25px rgba(25, 135, 84, 0.4);
}

.stats-card-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #0aa2c0 100%);
    box-shadow: 0 4px 15px rgba(13, 202, 240, 0.3);
}

.stats-card-info:hover {
    box-shadow: 0 8px 25px rgba(13, 202, 240, 0.4);
}

.stats-card-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #cc9a06 100%);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    color: #212529;
}

.stats-card-warning:hover {
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
}

.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card.bg-primary:hover,
.card.bg-success:hover,
.card.bg-info:hover,
.card.bg-warning:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #0dcaf0 0%, #31d2f2 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ffca2c 100%);
    border: none;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.action-buttons .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Tables */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-color: #f1f3f4;
}

/* Avatar Circle */
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Progress Bars */
.progress {
    height: 0.75rem;
    border-radius: 0.5rem;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    border-radius: 0.5rem;
    transition: width 0.6s ease;
}

/* Form Enhancements */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-control {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:hover {
    border-color: #adb5bd;
}

/* Input Groups */
.input-group-text {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #ced4da;
    color: #6c757d;
    font-weight: 500;
}

/* Badges */
.badge {
    font-size: 0.75em;
    padding: 0.35em 0.65em;
    border-radius: 0.375rem;
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #f1f3f4;
    border-radius: 0.75rem 0.75rem 0 0;
}

.modal-footer {
    border-top: 1px solid #f1f3f4;
    border-radius: 0 0 0.75rem 0.75rem;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Loading States */
.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Login Page Enhancements */
.min-vh-100 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.min-vh-100::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.login-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

/* Hover Effects for Login */
.input-group:hover {
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

.badge:hover {
    transform: scale(1.05);
    transition: all 0.2s ease;
}

/* Pulse Animation for Icons */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.display-1 {
    animation: pulse 2s infinite;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: 0.5rem;
    }
    
    .min-vh-100 {
        padding: 1rem;
    }
    
    .display-1 {
        font-size: 4rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .login-card {
        background: rgba(33, 37, 41, 0.95);
        color: #fff;
    }
    
    .card-header.bg-primary {
        background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%) !important;
    }
}

/* تحسينات الألوان والنصوص */
:root {
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-muted: #95a5a6;
    --bg-light: #f8f9fa;
    --bg-dark: #2c3e50;
    --border-light: #e9ecef;
    --border-dark: #495057;
}

[data-bs-theme="dark"] {
    --text-primary: #ecf0f1;
    --text-secondary: #bdc3c7;
    --text-muted: #95a5a6;
    --bg-light: #34495e;
    --bg-dark: #2c3e50;
    --border-light: #495057;
    --border-dark: #6c757d;
}

/* تحسين النصوص */
.text-primary-custom {
    color: var(--text-primary) !important;
}

.text-secondary-custom {
    color: var(--text-secondary) !important;
}

.text-muted-custom {
    color: var(--text-muted) !important;
}

.bg-light-custom {
    background-color: var(--bg-light) !important;
}

.bg-dark-custom {
    background-color: var(--bg-dark) !important;
}

.border-light-custom {
    border-color: var(--border-light) !important;
}

/* تحسين الكروت للوضع المظلم */
[data-bs-theme="dark"] .card {
    background: rgba(52, 73, 94, 0.95);
    border: 1px solid var(--border-dark);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .card-header {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    border-bottom: 1px solid var(--border-dark);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .card-footer {
    background: rgba(52, 73, 94, 0.8);
    border-top: 1px solid var(--border-dark);
}

/* تحسين الجداول للوضع المظلم */
[data-bs-theme="dark"] .table {
    color: var(--text-primary);
}

[data-bs-theme="dark"] .table th {
    background-color: #34495e;
    border-color: var(--border-dark);
}

[data-bs-theme="dark"] .table td {
    border-color: var(--border-light);
}

[data-bs-theme="dark"] .table-hover tbody tr:hover {
    background-color: rgba(52, 73, 94, 0.5);
}

/* تحسين النماذج للوضع المظلم */
[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select {
    background-color: #34495e;
    border-color: var(--border-dark);
    color: var(--text-primary);
}

[data-bs-theme="dark"] .form-control:focus,
[data-bs-theme="dark"] .form-select:focus {
    background-color: #34495e;
    border-color: #3498db;
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

[data-bs-theme="dark"] .input-group-text {
    background-color: #34495e;
    border-color: var(--border-dark);
    color: var(--text-secondary);
}

/* تحسين الشارات للوضع المظلم */
[data-bs-theme="dark"] .badge.bg-light {
    background-color: #34495e !important;
    color: var(--text-primary) !important;
}

/* تحسين التنبيهات للوضع المظلم */
[data-bs-theme="dark"] .alert-info {
    background-color: rgba(52, 152, 219, 0.2);
    border-color: #3498db;
    color: #85c1e9;
}

[data-bs-theme="dark"] .alert-success {
    background-color: rgba(39, 174, 96, 0.2);
    border-color: #27ae60;
    color: #82e0aa;
}

[data-bs-theme="dark"] .alert-warning {
    background-color: rgba(241, 196, 15, 0.2);
    border-color: #f1c40f;
    color: #f7dc6f;
}

[data-bs-theme="dark"] .alert-danger {
    background-color: rgba(231, 76, 60, 0.2);
    border-color: #e74c3c;
    color: #f1948a;
}

/* Progress Bars */
.progress {
    height: 0.75rem;
    border-radius: 0.5rem;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 0.5rem;
    transition: width 0.6s ease;
}

/* Tables */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

/* Forms */
.form-control {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
    border-radius: 0.375rem;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d1edff 0%, #a7d8f0 100%);
    color: #0c5460;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #b8daff 100%);
    color: #0c5460;
}

/* Custom Utilities */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--primary-color), var(--info-color)) 1;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* File Upload Area */
.file-upload-area {
    border: 2px dashed #ced4da;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--success-color);
    background-color: rgba(25, 135, 84, 0.05);
}

/* Loading Spinner */
.spinner-custom {
    width: 2rem;
    height: 2rem;
    border: 0.25em solid rgba(13, 110, 253, 0.25);
    border-right-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 0.75s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}